import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getFilterSchemaFor,
  getWhereSchemaFor,
  patch,
  del,
  requestBody,
  HttpErrors,
  RestBindings,
  RequestContext, Request,Response
} from '@loopback/rest';
import {<%= modelName %>} from '../models';
import {<%= repositoryName %>} from '../repositories';
import {
  AuthenticationBindings,
  UserProfile,
  authenticate,
} from '@loopback/authentication';
import {inject,intercept,InvocationContext,ValueOrPromise,InvocationResult,Interceptor} from '@loopback/context';
import {importExcel} from '../importExcel';

const log: Interceptor = async (invocationCtx:any, next:any) => {
const recv_timestmap = new Date().getTime();
let result = await next();
if (Array.isArray(result)) {
} else {
const resp_timestmap = new Date().getTime();
if(!result)
result={};
result.api_monit_info = {};
result.api_monit_info.recv_timestmap = recv_timestmap;
result.api_monit_info.db_request_exhaust = resp_timestmap - recv_timestmap;
result.api_monit_info.resp_timestmap = resp_timestmap;
if(result.data){
result.data = js_traverse(result.data);
} else if(invocationCtx.methodName.indexOf('download')<0) {
result = js_traverse(result);
}
}
return result;
};

@intercept(log)
export class <%= className %>Controller {
  constructor(
    @repository(<%= repositoryName %>)
    public <%= repositoryNameCamel %> : <%= repositoryName %>,
  ) {}
/*
async intercept(
invocationCtx: InvocationContext,
next: () => ValueOrPromise<InvocationResult>,
    ) {
    const recv_timestmap = new Date().getTime();
    const result = await next();
    if (Array.isArray(result)) {
    } else {
    const resp_timestmap = new Date().getTime();
    result.api_monit_info = {};
    result.api_monit_info.recv_timestmap = resp_timestmap - recv_timestmap;
    result.api_monit_info.resp_timestmap = resp_timestmap;
    if(result.data){
    result.data = js_traverse(result.data);
    }
    }
    return result;
    }
*/
  <%_ Object.entries(api).forEach(([path, val]) => { -%>
    <%_ if (val['type'] === 'preset') { -%>

      <%_ if (val['name'] === 'create') { -%>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @post('<%= val['path'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: '<%= modelName %> model instance',
              content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.create"
        })
        async create(@requestBody() <%= modelVariableName %>: <%= modelName %>): Promise<<%= modelName %>> {
          return await this.<%= repositoryNameCamel %>.create(<%= modelVariableName %>);
        }


        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'getBatchTemplate',
            result:'Document',
            description:'Download batch import excel template',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>/batch',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @get('<%= val['pathTpl'] %>/batch', {
            summary: 'Download batch import excel template',
            tags:['<%= name %>'],
            responses: {
                '200': {
                    description: 'Download batch import excel template',
                    content: {'application/octet-stream': {}},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.getBatchTemplate"
        })
        async getBatchTemplate(@inject(RestBindings.Http.CONTEXT) ctx: RequestContext): Promise<object> {

            let properties = this.<%= repositoryNameCamel %>.entityClass.definition.properties;
            let modelName = this.<%= repositoryNameCamel %>.entityClass.definition.name;
            let fields = Object.keys(properties).map(function(name){
            // @ts-ignore
            let title = properties[name].jsonSchema && properties[name].jsonSchema.title || '';
                return title ? `${title}(${name})` : name;
            });

            ctx.response.setHeader('Content-Disposition', 'attachment; filename="' + modelName + '.xlsx"');

            return importExcel.generatorTemplate([fields], `${modelName} records`);
        }




        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'getBatchTemplate',
            result:'Document',
            description:'Batch import excel records',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>/batch',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @post('<%= val['pathTpl'] %>/batch', {
            summary: 'Batch import excel records',
            tags:['<%= name %>'],
            responses: {
            '200': {
            description: 'Batch import excel records',
                content: {'application/json': {
                    schema: {type: 'object', properties: {
                        total: { type: 'number' },
                            details: { type: 'object'}
                        }},
                    }},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.batchImport"
        })
        async batchImport(
            @requestBody({
                description: 'multipart/form-data value.',
                required: true,
                content: {
                    'multipart/form-data': {
                        'x-parser': 'stream',
                        schema: {type: 'object'},
                    },
                },
            })
            request: Request,
        @inject(RestBindings.Http.CONTEXT)
        ctx: RequestContext): Promise<{total: 0, details?: {}}> {
            let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');

            const multer = require('multer');
            const upload = multer({
                dest: tmpdir,
                limits: {
                    fileSize: 1024*1024*100
                },
                fileFilter: (req: any, file: any, cb: any) => {
                    let originalName = file.originalname || '';
                    if( originalName.lastIndexOf('.xlsx') >= 0 ) {
                        cb(null, true);
                    } else {
                        cb(null, false);
                    }
                }
            });

            return new Promise<{total: 0}>((resolve, reject) => {

                upload.any()(request, ctx.response, (err: any) => {
                if( err ){
                    reject(err);
                } else {
                    // @ts-ignore
                    let files: any[] = request.files;

                    let filePaths = files.map( file => file.path);

                    importExcel.readFromFiles(filePaths).then((result) => {

                        let total: number = 0;
                        let promises: Promise<<%= modelName %>>[] = [];
                        Object.keys(result).forEach(file => {

                        // @ts-ignore
                        Object.keys(result[file]).forEach(sheet => {

                            // @ts-ignore
                            total += result[file][sheet].length;

                            // @ts-ignore
                            promises.push(this.<%= repositoryNameCamel %>.createAll(result[file][sheet]));
                        });
                    });

                    Promise.all(promises).then((results => {

                        // @ts-ignore
                        resolve({ total: total })

                    })).catch(reject);

                    }).catch(reject);
                }
            });
        });
    }





    <%_ } else if (val['name'] === 'findPage') { %>

            @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
            })
            @get('<%= val['path'] %>/count', {
            summary: 'count data',
            tags:['<%= name %>'],
            responses: {
            '200': {
            description: 'Object of count data, result.total is model count.',
            content: {
            'application/json': {
            schema: {type: 'object', properties: {
            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
            total: { type: 'object', properties: { count: { type: 'number' } } }
            }},
            },
            },
            },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.list"
            })
            async count(
            @param.query.object('filter', getWhereSchemaFor(<%= modelName %>)) filter?: any
            ): Promise<{total: Count}> {
            filter = filter || {};


            let total = await this.<%= repositoryNameCamel %>.count(filter);
            return {
            total: total
            };
            }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @get('<%= val['path'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
              content: {
                'application/json': {
                  schema: {type: 'object', properties: {
                    data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                    total: { type: 'object', properties: { count: { type: 'number' } } }
                  }},
                },
              },
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.list"
        })
        async findPage(
          @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
          @param.query.string('filename') filename?: String,
            @param.query.string('page') userpage?: String,
            @param.query.string('limit') userlimit?: String,
        ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    // Convert string values to proper boolean/number values for LoopBack fields filter
                    if (filter.fields[key] === "false" || filter.fields[key] === false || filter.fields[key] === "0" || filter.fields[key] === 0) {
                        filter.fields[key] = false;
                    } else if (filter.fields[key] === "true" || filter.fields[key] === true || filter.fields[key] === "1" || filter.fields[key] === 1) {
                        filter.fields[key] = true;
                    }
                })
            }

            <%_ if (val['fields']) { -%>
                filter.fields = filter.fields || {};
                Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
            <%_ } -%>

          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
              filter.limit = <%= config.get('defaultLimit') || 10 %>;
            }
          }
          <%_ if(config.get('maxLimit')) { -%>
            filter.limit = Number(filter.limit) > <%= config.get('maxLimit') %> ? <%= config.get('maxLimit') %> : Number(filter.limit);
          <%_ } -%>
            if( userlimit && userlimit !== ''){
            filter.limit = Number(userlimit)
            }
            if( userpage && userpage !== ''){
            // @ts-ignore
            filter.skip = filter.limit * (Number(userpage) - 1);
            }
          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }
            let data = await this.<%= repositoryNameCamel %>.find(filter);
            let total = await this.<%= repositoryNameCamel %>.count(filter['where'] || {});
            //let where = {};
            //if(filter['where']){
            //where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
            //}
            //let total = await this.<%= repositoryNameCamel %>.count(where);
            return {
              data,
              total
            };
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @post('<%= val['path'] %>/find', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '200': {
                    description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
                    content: {
                        'application/json': {
                            schema: {type: 'object', properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }},
                    },
                },
            },
        },
        "x-table-name": "<%=tableName%>",
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async findPage_post( @requestBody() filter?: Filter<<%= modelName %>>,
            ): Promise<{data: <%= modelName %>[], total:Count}> {
            filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    // Convert string values to proper boolean/number values for LoopBack fields filter
                    if (filter.fields[key] === "false" || filter.fields[key] === false || filter.fields[key] === "0" || filter.fields[key] === 0) {
                        filter.fields[key] = false;
                    } else if (filter.fields[key] === "true" || filter.fields[key] === true || filter.fields[key] === "1" || filter.fields[key] === 1) {
                        filter.fields[key] = true;
                    }
                })
            }

            <%_ if (val['fields']) { -%>
                filter.fields = filter.fields || {};
                Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
            <%_ } -%>

            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
                filter.limit = <%= config.get('defaultLimit') || 10 %>;
            }
            <%_ if(config.get('maxLimit')) { -%>
                filter.limit = Number(filter.limit) > <%= config.get('maxLimit') %> ? <%= config.get('maxLimit') %> : Number(filter.limit);
            <%_ } -%>
            const rawWhere = (filter.where || {});
            let requires: string[] = [] ;
            let available: string[] = [] ;

            <%_ if (val['requiredQueryField']) { -%>
                requires = <%- JSON.stringify(val['requiredQueryField']) %>;
            <%_ } -%>

            <%_ if (val['availableQueryField']) { -%>
                available = <%- JSON.stringify(val['availableQueryField']) %>;
            <%_ } -%>

            let allField: string[] = [];
            this.findAllField(rawWhere, allField);

            if (requires.length > 0) {
                const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
                let ok = false;
                for (let index = 0; index < requires.length; index++) {
                    const er = requires[index];
                    ok = allField.includes(er);
                    if (!ok) { break }
                }
                if (!ok) {
                    throw err;
                }
            }
            // @ts-ignore
            if(filter && filter.page && filter.limit){
            // @ts-ignore
            filter.skip = (filter.page - 1) * filter.limit;
            }
            if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
                for (let index = 0; index < allField.length; index++) {
                    const er = allField[index];
                    ok = available.includes(er);
                    if (!ok) { break }
                }
                if (!ok) {
                    throw err;
                }
            }

            let data = await this.<%= repositoryNameCamel %>.find(filter);
            //let where = {};
            //if(filter['where']){
            //where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
            //}
            let total = await this.<%= repositoryNameCamel %>.count(filter['where'] || {});
            return {
              data,
              total
            };
        }

      <%_ } else if (val['name'] === 'findById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @get('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: '<%= modelName %> model instance',
              content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.get"
        })
        async findById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<<%= modelName %>> {
          return await this.<%= repositoryNameCamel %>.findById(id);
        }
      <%_ } else if (val['name'] === 'updateById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @patch('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '204': {
              description: '<%= modelName %> PATCH success',
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.update"
        })
        async updateById(
          @param.path.<%= idType %>('id') id: <%= idType %>,
          @requestBody() <%= modelVariableName %>: <%= modelName %>,
        ): Promise<void> {
          await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @post('<%= val['path'] %>/{id}', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '204': {
                description: '<%= modelName %> PATCH success',
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>"
        })
        async updateById_post(
            @param.path.<%= idType %>('id') id: <%= idType %>,
            @requestBody() <%= modelVariableName %>: <%= modelName %>,
            ): Promise<void> {
            await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
        }



        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @patch('<%= val['path'] %>', {
            summary: 'update all match document with where',
            tags:['<%= name %>'],
            responses: {
            '204': {
            description: '<%= modelName %> PATCH success',
            },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.updateAll"
        })
        async updateAll(
        @requestBody() <%= modelVariableName %>: <%= modelName %>,
        @param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
        ): Promise<void> {
            await this.<%= repositoryNameCamel %>.updateAll(<%= modelVariableName %>, where);
         }
      <%_ } else if (val['name'] === 'deleteById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @del('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '204': {
              description: '<%= modelName %> DELETE success',
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.delete"
        })
        async deleteById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
          await this.<%= repositoryNameCamel %>.deleteById(id);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>/delete',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @get('<%= val['path'] %>/{id}/delete', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '204': {
                    description: '<%= modelName %> DELETE success',
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>"
        })
        async deleteById_get(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
            await this.<%= repositoryNameCamel %>.deleteById(id);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'deleteWithWhere',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
		@del('<%= val['path'] %>', {
			summary: 'Delete all match document with where',
            tags:['<%= name %>'],
			responses: {
				'204': {
					description: '<%= modelName %> DELETE success',
				},
			},
			"x-table-name": "<%=tableName%>",
			"x-api-id": "<%=apiId%>",
			"x-api-name": "<%=apiName%>",
			"x-bsm-operation": "<%= modelName %>.deleteWithWhere"
		})
        async deleteWithWhere(
                    @param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
        ) {
            await this.<%= repositoryNameCamel %>.deleteAll(where);
        }

      <%_ } else if (val['name'] === 'downloadById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @get('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: 'file stream',
              content: {'application/octet-stream ': {}},
            },
            '404': {
                description: 'file not found'
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.downloadById",
        })
        async downloadById(@param.path.<%= idType %>('id') id: <%= idType %>,@inject(RestBindings.Http.RESPONSE) res: Response,): Promise<any> {
                                // @ts-ignore
                                res.__fun ="download";
          let file : any = await this.<%= repositoryNameCamel %>.findById(id);
          if(!file){
            throw new HttpErrors.NotFound("File not found");
          }
          let filename = file.filename ? file.filename.split('/') : ['file'];
          filename = filename[filename.length - 1]
          if( file.metadata && file.metadata.file_name){
            filename = file.metadata.file_name
          }
          if( file.metadata && file.metadata.file_extension){
            filename = `${filename}.${file.metadata.file_extension}`
          }
          return {
            filename: encodeURIComponent(filename),
            stream: await this.<%= repositoryNameCamel %>.downloadById(id)
          };
        }

      <%_ } else if (val['name'] === 'download') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
          })
        @get('<%= val['path'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
            responses: {
              '200': {
                  description: 'file stream',
                  content: {'application/octet-stream ': {}},
                },
                '404': {
                    description: 'file not found'
                },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.download"
        })
        async download(
          @inject(RestBindings.Http.RESPONSE) res: Response,
          @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
        ): Promise<any> {
                                    // @ts-ignore
                                    res.__fun ="download";
          let files = await this.<%= repositoryNameCamel %>.find(filter);
          let file : any = files && files.length > 0 ? files[0] : null;
          if( file ){
            let filename = file.filename ? file.filename.split('/') : ['file'];
            filename = filename[filename.length - 1]
            if( file.metadata && file.metadata.file_name){
              filename = file.metadata.file_name
            }
            if( file.metadata && file.metadata.file_extension){
              filename = `${filename}.${file.metadata.file_extension}`
            }
            return {
              filename: encodeURIComponent(filename),
              stream: await this.<%= repositoryNameCamel %>.downloadById(file._id || '')
            };
          } else {
            throw new HttpErrors.NotFound("File not found");
          }
        }
      <%_ } else if (val['name'] === 'upload') { %>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @post('/api/v1/files/upload', {
            summary: '<%= val['summary'] %>',
            tags:['<%= name %>'],
            responses: {
            '200': {
                content: {'application/json': {
                    schema: {type: 'object', properties: {
                        total: { type: 'number' },
                            details: { type: 'object'}
                        }},
                    }},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.upload"
        })
        async upload(
            @requestBody({
                description: 'multipart/form-data value.',
                required: true,
                content: {
                    'multipart/form-data': {
                        'x-parser': 'stream',
                        schema: {type: 'object'},
                    },
                },
            })
            request: Request,
            @inject(RestBindings.Http.CONTEXT)
            ctx: RequestContext): Promise<{total: 0, ids?: {}}> {
                let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');

                const multer = require('multer');
                const upload = multer({
                    dest: tmpdir,
                    limits: {
                        fileSize: 1024*1024*100
                    }
                });

                return new Promise<{total: 0}>((resolve, reject) => {

                    upload.any()(request, ctx.response, (err: any) => {
                    if( err ){
                        reject(err);
                    } else {
                        // @ts-ignore
                        let files: any[] = request.files;

                        let count: number = files.length;
                        let promises: Promise<any>[] = [];
                        files.forEach(( file => {
                            promises.push(this.<%= repositoryNameCamel %>.upload(file));
                        }));
                        Promise.all(promises).then((results => {

                            // @ts-ignore
                            resolve({ count: count, ids: results })

                        })).catch(reject);

                    }
                });
            });
        }
      <%_ }  %>
<%_ } else if (val['name'] === 'customerQuery') { %>

    @authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>customerQuery',
    result:'<%= val['result'] %>customerQuery',
    description:'<%= val['description'] %> customerQuery',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>
    })
    @get('<%= val['path'] %>', {
    summary: 'Customer Query',
    tags:['<%= name %>'],
    responses: {
    '200': {
    description: 'Object of page data, result.data is model instances, result.total is model count.',
    content: {
    'application/json': {
    schema: {type: 'object', properties: {
    data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
    total: { type: 'object', properties: { count: { type: 'number' } } }
    }},
    },
    },
    },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
    })
    async customerQuery(
      <% const paramTypeMapping = {date:'string',datetime:'string',time:'string'} %>
      <% for(let parameter in val.parameters) { %>
      <% const paramType = paramTypeMapping[val.parameters[parameter].type] || val.parameters[parameter].type %>
    @param.query.string('<%-val.parameters[parameter].name%>') tapdata_<%-val.parameters[parameter].name%> : <%-paramType%>,
      <% } %>
    ): Promise<{data: <%= modelName %>[], total:Count}>{
                                            let params = {};
                                            const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined){
                                              if(value === undefined || value === null){
                                                return value;
                                              }
                                              if(field && field.type && field.type === 'string'){
                                                return value.toString();
                                              }
                                              if(field && field.type && field.type === 'number'){
                                                // @ts-ignore
                                                const nValue = value * 1;
                                                if(nValue.toString() !== value.toString()){
                                            throw new Error(('is not a number'));
                                                }
                                                return nValue
                                              }
                                              if(field && field.type && field.type === 'date'){
                                            // @ts-ignore
                                            if(value.replace(/-/g,'').length !== 8){
                                            throw new Error(('is not a date'));
                                            }
                                                try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
                                                }catch(e){
                                            throw new Error(('is not a date'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'datetime'){
                                                try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
                                                }catch(e){
                                            throw new Error(('is not a datetime'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'time'){
                                                try{
                                            // @ts-ignore
                                            let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
                                            if(a === null || (a[1]>24 || a[3]>60 || a[4]>60)){
                                            throw new Error(('is not a time'));
                                            }
                                                  return value
                                                }catch(e){
                                            throw new Error(('is not a time'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'boolean'){
                                                if(value === true || value === 'true' || value === 1 || value === '1'){
                                                  return true;
                                                }
                                                if(value === false || value === 'false' || value === 0 || value === '0'){
                                                  return false;
                                                }
                                            throw new Error(('is not a Boolean'));
                                              }
                                              return value;
                                            }
                                            <% for(let parameter in val.parameters) { %>
                                            // @ts-ignore
                                            <% if(val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== ""){%>
                                            if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number'){
                                            // @ts-ignore
                                            tapdata_<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
                                            }else if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined){
                                            // @ts-ignore
                                                tapdata_<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
                                              }
                                             <%}%>
                                              // @ts-ignore
                                              params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},tapdata_<%-val.parameters[parameter].name%>);
                                            <% } %>
        // tslint:disable-next-line:no-any
        let filter:any = {where:null,sort:[]};
                                            // tslint:disable-next-line:no-any
                                            let where:any = <%-val.whereString%>;
                                            for(let x in where){
                                            // @ts-ignore
                                            if(params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined){
                                            // @ts-ignore
                                            let nWhere = {};
                                            if(where[x].operator === '=='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = params[where[x].parameter]
                                            } else if (where[x].operator === '>'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
                                            } else if (where[x].operator === '>='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
                                            } else if (where[x].operator === '<'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
                                            } else if (where[x].operator === '<='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
                                            } else if (where[x].operator === '!='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
                                            } else if (where[x].operator === 'like'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
                                            }
                                            // @ts-ignore
                                            if(!filter.where){
                                              filter.where = nWhere
                                            }else if(where[x].condition === 'or'){
                                              filter.where = {'or':[filter.where,nWhere]}
                                            } else {
                                              filter.where = {'and':[filter.where,nWhere]}
                                            }
                                            //filter.where[where[x]['fieldName']] = params[where[x].parameter]
                                            }
                                            }
                                            filter.order = <%-val.sortString%>;
                                            <% if(val.select && val.select.length>0){%>
                                            filter.fields = <%-val.selectString%>;
                                            <%}%>
                                            filter.limit = tapdata_limit || 100;
                                            if(tapdata_limit && tapdata_page){
                                            filter.skip = tapdata_limit * (tapdata_page -1);
                                            }
                                            if(filter.order.length === 0){
                                            delete filter.order
                                            }
                                            if(filter.where === null){
                                            delete filter.where
                                            }
        let data = await this.<%= repositoryNameCamel %>.find(filter);
        let total = await this.<%= repositoryNameCamel %>.count(filter['where'] || {});
        return {
        data,
        total
        };
        }


        @authenticate('JwtStrategy', {
        allPathId:'<%= val['allPathId'] %>',
        rawName:'<%= val['rawName'] %>customerQuery',
        result:'<%= val['result'] %>customerQuery',
        description:'<%= val['description'] %> customerQuery',
        type:'<%= val['type'] %>',
        pathTpl:'<%= val['pathTpl'] %>',
        roles: <%- JSON.stringify(val['roles'])%>
        })
        @post('<%= val['path'] %>/find', {
        summary: 'Customer Query',
        tags:['<%= name %>'],
        responses: {
        '200': {
        description: 'Object of page data, result.data is model instances, result.total is model count.',
        content: {
        'application/json': {
        schema: {type: 'object', properties: {
        data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
        total: { type: 'object', properties: { count: { type: 'number' } } }
        }},
        },
        },
        },
        },
        "x-table-name": "<%=tableName%>",
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async customerQuery_post(
                                            @requestBody() body?: any
        ): Promise<{data: <%= modelName %>[], total:Count}>{
        let params = {};
        const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined){
        if(value === undefined || value === null){
        return value;
        }
        if(field && field.type && field.type === 'string'){
        return value.toString();
        }
        if(field && field.type && field.type === 'number'){
        // @ts-ignore
        const nValue = value * 1;
        if(nValue.toString() !== value.toString()){
        throw new Error(('is not a number'));
        }
        return nValue
        }
        if(field && field.type && field.type === 'date'){
        // @ts-ignore
        if(value.replace(/-/g,'').length !== 8){
        throw new Error(('is not a date'));
        }
        try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
        }catch(e){
        throw new Error(('is not a date'));
        }
        }
        if(field && field.type && field.type === 'datetime'){
        try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
        }catch(e){
        throw new Error(('is not a datetime'));
        }
        }
        if(field && field.type && field.type === 'time'){
        try{
        // @ts-ignore
        let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
        if(a === null || (a[1]>24 || a[3]>60 || a[4]>60)){
        throw new Error(('is not a time'));
        }
        return value
        }catch(e){
        throw new Error(('is not a time'));
        }
        }
        if(field && field.type && field.type === 'boolean'){
        if(value === true || value === 'true' || value === 1 || value === '1'){
        return true;
        }
        if(value === false || value === 'false' || value === 0 || value === '0'){
        return false;
        }
        throw new Error(('is not a Boolean'));
        }
        return value;
        }
        <% for(let parameter in val.parameters) { %>
            // @ts-ignore
            <% if(val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== ""){%>
            if(body.<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number'){
            // @ts-ignore
                                                body.<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
            }else if(body.<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined){
            // @ts-ignore
                                                body.<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
            }
            <%}%>
            // @ts-ignore
            params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},body.<%-val.parameters[parameter].name%>);
        <% } %>
        // tslint:disable-next-line:no-any
        let filter:any = {where:null,sort:[]};
                                            // tslint:disable-next-line:no-any
        let where:any = <%-val.whereString%>;
        for(let x in where){
        // @ts-ignore
        if(params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined){
        // @ts-ignore
        let nWhere = {};
        if(where[x].operator === '=='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = params[where[x].parameter]
        } else if (where[x].operator === '>'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
        } else if (where[x].operator === '>='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
        } else if (where[x].operator === '<'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
        } else if (where[x].operator === '<='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
        } else if (where[x].operator === '!='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
        } else if (where[x].operator === 'like'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
        }
        // @ts-ignore
        if(!filter.where){
            filter.where = nWhere
        }else if(where[x].condition === 'or'){
        filter.where = {'or':[filter.where,nWhere]}
        } else {
        filter.where = {'and':[filter.where,nWhere]}
        }
        //filter.where[where[x]['fieldName']] = params[where[x].parameter]
        }
        }
        filter.order = <%-val.sortString%>;
        <% if(val.select && val.select.length>0){%>
        filter.fields = <%-val.selectString%>;
        <%}%>
        filter.limit = body.tapdata_limit || 100;
        if(body.tapdata_limit && body.tapdata_page){
        filter.skip = body.tapdata_limit * (body.tapdata_page -1);
        }
        if(filter.order.length === 0){
          delete filter.order
        }
        if(filter.where === null){
          delete filter.where
        }
        let data = await this.<%= repositoryNameCamel %>.find(filter);
        let total = await this.<%= repositoryNameCamel %>.count(filter['where'] || {});
        return {
        data,
        total
        };
       }
    <%_ } else if (val['method'] === 'STREAM') { %>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>
        })
        @get('<%= val['path'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
        '200': {
        description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
        content: {
        'application/json': {
        schema: {type: 'object', properties: {
        data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
        total: { type: 'object', properties: { count: { type: 'number' } } }
        }},
        },
        },
        },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async <%= val['name'] %>(): Promise<{}> {
        return {
        "name": "rrr",
        "connector": "<%=dataSource.dataSourceType%>",
        "allowExtendedOperators": true,
        "useNewUrlParser": true,
        "url": "<%=dataSource.settings.url%>",
        "ssl": <%=dataSource.settings.ssl%>,
        "sslCA": "<%=dataSource.settings.sslCA%>",
        <%_ if (val['filter']) { -%>
            "filter":this.<%= repositoryNameCamel %>.dataSource.adapter.buildWhere('<%= tableName %>',<%- JSON.stringify(val['filter']) %>),
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            "fields":<%- JSON.stringify(val['fields']) %>,
        <%_ } -%>
        "collection":"<%=tableName%>"
        };
        }
    <%_ } else {-%>

      @authenticate('JwtStrategy', {
          allPathId:'<%= val['allPathId'] %>',
          rawName:'<%= val['rawName'] %>',
          result:'<%= val['result'] %>',
          description:'<%= val['description'] %>',
          type:'<%= val['type'] %>',
          pathTpl:'<%= val['pathTpl'] %>',
          roles: <%- JSON.stringify(val['roles'])%>
        })
      @get('<%= val['path'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
          '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
              'application/json': {
                schema: {type: 'object', properties: {
                  data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                  total: { type: 'object', properties: { count: { type: 'number' } } }
                }},
              },
            },
          },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
      })
      async <%= val['name'] %>(
        @param.query.object('filter', getFilterSchemaFor(<%= modelName %>),ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'GET')) filter?: Filter<<%= modelName %>>,
        @param.query.string('filename') filename?: String,
      ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    // Convert string values to proper boolean/number values for LoopBack fields filter
                    if (filter.fields[key] === "false" || filter.fields[key] === false || filter.fields[key] === "0" || filter.fields[key] === 0) {
                        filter.fields[key] = false;
                    } else if (filter.fields[key] === "true" || filter.fields[key] === true || filter.fields[key] === "1" || filter.fields[key] === 1) {
                        filter.fields[key] = true;
                    }
                })
            }

          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
              filter.limit = <%= config.get('defaultLimit') || 10 %>;
            }
          }
        <%_ if(config.get('maxLimit')) { -%>
          filter.limit = Number(filter.limit) > <%= config.get('maxLimit') %> ? <%= config.get('maxLimit') %> : Number(filter.limit);
        <%_ } -%>


          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

        <%_ if (val['filter']) { -%>
            filter.where = filter.where || {};
            Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            filter.fields = filter.fields || {};
            Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>

          return {
            data: await this.<%= repositoryNameCamel %>.find(filter),
            total: await this.<%= repositoryNameCamel %>.count(filter['where'] || {})
          };
      }

      @authenticate('JwtStrategy', {
          allPathId:'<%= val['allPathId'] %>',
          rawName:'<%= val['rawName'] %>',
          result:'<%= val['result'] %>',
          description:'<%= val['description'] %>',
          type:'<%= val['type'] %>',
          pathTpl:'<%= val['pathTpl'] %>',
          roles: <%- JSON.stringify(val['roles'])%>
        })
      @post('<%= val['path'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
          '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
              'application/json': {
                schema: {type: 'object', properties: {
                  data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                  total: { type: 'object', properties: { count: { type: 'number' } } }
                }},
              },
            },
          },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>_post.list"
      })
      async <%= val['name'] %>_post(
        @requestBody(ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'POST')) filter?: Filter<<%= modelName %>>,
        @param.query.string('filename') filename?: String,
      ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    // Convert string values to proper boolean/number values for LoopBack fields filter
                    if (filter.fields[key] === "false" || filter.fields[key] === false || filter.fields[key] === "0" || filter.fields[key] === 0) {
                        filter.fields[key] = false;
                    } else if (filter.fields[key] === "true" || filter.fields[key] === true || filter.fields[key] === "1" || filter.fields[key] === 1) {
                        filter.fields[key] = true;
                    }
                })
            }

          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
              filter.limit = <%= config.get('defaultLimit') || 10 %>;
            }
          }
        <%_ if(config.get('maxLimit')) { -%>
          filter.limit = Number(filter.limit) > <%= config.get('maxLimit') %> ? <%= config.get('maxLimit') %> : Number(filter.limit);
        <%_ } -%>


          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

        <%_ if (val['filter']) { -%>
            filter.where = filter.where || {};
            Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            filter.fields = filter.fields || {};
            Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>

          return {
            data: await this.<%= repositoryNameCamel %>.find(filter),
            total: await this.<%= repositoryNameCamel %>.count(filter['where'] || {})
          };
      }

    <%_ } -%>
  <%_ }) -%>

  // @ts-ignore
  findAllField(where, allField) {
    if (!where || !allField) { return }
    const oa = where.or || where.and;
    if (oa && Object.prototype.toString.call(oa) === '[object Array]') {
      oa.forEach((item:any) => {
        this.findAllField(item, allField);
      });
    } else {
      for (const key in where) {
        if (key && where.hasOwnProperty(key)) {
          allField.push(key);
        }
      }
    }
  }

}

function js_traverse(o:any) {
  let type = typeof o
  if (type === "object") {
    for (let key in o) {
        let itemType = typeof o[key];
        if (itemType === "object" && o[key] && "Decimal128" === o[key]._bsontype) {
         o[key] = o[key].toString();
         continue;
        }
      else if(key !== '__proto__' && o.hasOwnProperty(key)){
        try{
          o[key] = js_traverse(o[key])
        }catch(e){
          console.error(e);
        }
      }
    }
  } else {
  }
  return o;
}

function ccc(str:any,str1:any,type:string){
    let des = '';
    let restexp = '';
    if(str1&& str1.length>0){
    str1 = str1.split(',');
    des += '<b>Required query parameters, you must include these parameters in your query string.</b>';//Required query fields:
    for(let i=0;i<str1.length;i++){
    des += "<br>&nbsp;&nbsp;" + str1[i] ;
    }
    des += "<br><br>";
    }

    if(str&& str.length>0){
    str = str.split(',');
    des += '<b>Allowed query parameters, you may use one or more parameters below in your query string.</b>';
    for(let i=0;i<str.length;i++){
    des += "<br>&nbsp;&nbsp;" + str[i] ;
                                    if(i===0){
                                    restexp += "<br>REST:<br>?filter[where]["+str[i].split(":")[0]+"]=FilterCondition<br>"
                                    }
    }

    des += '<br><br><b>Examples:</b>';
                                    if(restexp !== '' && type === 'GET')
                                    des += restexp;
    des += "<br>";
                                    if(type === 'GET'){
                                    des += "Encode filter object as JSON<br>"
                                    des += "?filter=";
                                    des += "{";
                                    des += '"where":{';
                                    let strArr = [];
                                    for(let i=0;i<str.length;i++){
                                    strArr.push('"' + str[i].split(':')[0] +'":'+ str[i].split(':')[1]);
                                    }
                                    des +=strArr.join(",");
                                    des += "}";
                                    des += "}";
                                    }else{
                                    des += "Use following as POST method body content:";
    des += "<br>{";
    des += "<br>&nbsp;&nbsp;where:{";
    for(let i=0;i<str.length;i++){
    des += "<br>&nbsp;&nbsp;&nbsp;&nbsp;" + str[i];
    }
    des += "<br>&nbsp;&nbsp;}";
    des += "<br>}";
                                    }
    }
    return { description:des};
}

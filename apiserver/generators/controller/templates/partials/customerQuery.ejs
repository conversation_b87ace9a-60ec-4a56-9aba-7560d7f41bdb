//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>customerQuery',
    result:'<%= val['result'] %>customerQuery',
    description:'<%= val['description'] %> customerQuery',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
    })
    @get('<%= val['path'] %><%= val['getSuffix'] %>', {
        summary: 'Customer Query',
        tags:['<%= name %>'],
        responses: {
            '200': {
            description: 'Object of page data, result.data is model instances, result.total is model count.',
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                    }},
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async customerQuery(
    <% const paramTypeMapping = {date:'string',datetime:'string',time:'string'} %>
    <% for(let parameter in val.parameters) { %>
        <% const paramType = paramTypeMapping[val.parameters[parameter].type] || val.parameters[parameter].type %>
        @param.query.string('<%-val.parameters[parameter].name%>') tapdata_<%-val.parameters[parameter].name%> : <%-paramType%>,
    <% } %>
): Promise<{data: <%= modelName %>[], total:Count}>{
    let params = {};
    const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined){
        if (value === undefined || value === null) {
            return value;
        }
        if (field && field.type && field.type === 'string') {
            return value.toString();
        }
        if (field && field.type && field.type === 'number') {
            // @ts-ignore
            const nValue = value * 1;
            if (nValue.toString() !== value.toString()) {
                throw new Error(('is not a number'));
            }
            return nValue
        }
        if (field && field.type && field.type === 'date') {
            // @ts-ignore
            if (value.replace(/-/g,'').length !== 8) {
                throw new Error(('is not a date'));
            }
            try {
                // @ts-ignore
                const d = new Date(value);
                if(d instanceof Date && !isNaN(d.getTime())) {
                    return d;
                }
                throw new Error(('is not a datetime'));
            } catch(e) {
                throw new Error(('is not a date'));
            }
        }
        if (field && field.type && field.type === 'datetime') {
            try {
                // @ts-ignore
                const d = new Date(value);
                if (d instanceof Date && !isNaN(d.getTime())) {
                    return d;
                }
                throw new Error(('is not a datetime'));
            } catch(e) {
                throw new Error(('is not a datetime'));
            }
        }
        if (field && field.type && field.type === 'time') {
            try {
                // @ts-ignore
                let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
                if(a === null || (a[1]>24 || a[3]>60 || a[4]>60)){
                    throw new Error(('is not a time'));
                }
                return value
            } catch(e) {
                throw new Error(('is not a time'));
            }
        }
        if (field && field.type && field.type === 'boolean') {
            if (value === true || value === 'true' || value === 1 || value === '1') {
              return true;
            }
            if (value === false || value === 'false' || value === 0 || value === '0') {
              return false;
            }
            throw new Error(('is not a Boolean'));
        }
        return value;
    }
    <% for(let parameter in val.parameters) { %>
        // @ts-ignore
        <% if(val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== ""){%>
            if (tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number') {
                // @ts-ignore
                tapdata_<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
            } else if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined) {
                // @ts-ignore
                tapdata_<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
            }
        <%}%>
        // @ts-ignore
        params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},tapdata_<%-val.parameters[parameter].name%>);
    <% } %>
    // tslint:disable-next-line:no-any
    let filter:any = {where:null,sort:[]};
    // tslint:disable-next-line:no-any
    let where:any = <%-val.whereString%>;
    <%_ if (val['fullCustomQuery']) { -%>
    filter.where = deepReplaceInPlace(JSON.stringify(where), params);
    <%_ } -%>
    <%_ if (!val['fullCustomQuery']) { -%>
    for (let x in where) {
        // @ts-ignore
        if (params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined) {
            continue;
        }
        // @ts-ignore
        let nWhere = {};
        switch(where[x].operator) {
            case '==':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = params[where[x].parameter]
                break;
            case '>':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
                break;
            case '>=':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
                break;
            case '<':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
                break;
            case '<=':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
                break;
            case '!=':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
                break;
            case 'like':
                // @ts-ignore
                nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
                break;
        }
        // @ts-ignore
        if (!filter.where) {
            filter.where = nWhere
        } else if(where[x].condition === 'or') {
            filter.where = {'or':[filter.where,nWhere]}
        } else {
            filter.where = {'and':[filter.where,nWhere]}
        }
        //filter.where[where[x]['fieldName']] = params[where[x].parameter]
    }
    <%_ } -%>
    filter.order = <%-val.sortString%>;
    <% if (val.select && val.select.length>0) {%>
    filter.fields = <%-val.selectString%>;
    <%}%>
    filter.limit = tapdata_limit || 100;
    if (tapdata_limit && tapdata_page) {
        filter.skip = tapdata_limit * (tapdata_page -1);
    }
    if (filter.order.length === 0) {
        delete filter.order
    }
    if (filter.where === null) {
        delete filter.where
    }
    let whereIs = {};
    if (filter['where']) {
        whereIs = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = whereIs || filter['where'] || {};
    let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (total <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, total};
    }
    let data = await this.<%= repositoryNameCamel %>.find(filter);
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
    let dataInfo = handleNameMappingAll(data, aliasNameMapping);
    //@ts-ignore
    return {dataInfo, total};
}


@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>customerQuery',
    result:'<%= val['result'] %>customerQuery',
    description:'<%= val['description'] %> customerQuery',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
        originalConfig: <%- JSON.stringify(originalConfig)%>
    })
    @post('<%= val['path'] %><%= val['postSuffix'] %>', {
        summary: 'Customer Query',
        tags:['<%= name %>'],
        responses: {
            '200': {
                description: 'Object of page data, result.data is model instances, result.total is model count.',
                content: {
                    'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }
                    },
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async customerQuery_post(
    @requestBody() requestData?: any,
): Promise<{data: <%= modelName %>[], total:Count}>{
    let params = {};
    const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined) {
        if(value === undefined || value === null){
            return value;
        }
        if (field && field.type && field.type === 'string') {
            return value.toString();
        }
        if (field && field.type && field.type === 'number') {
            // @ts-ignore
            const nValue = value * 1;
            if (nValue.toString() !== value.toString()) {
             throw new Error(('is not a number'));
            }
            return nValue
        }
        if (field && field.type && field.type === 'date') {
        // @ts-ignore
        if (value.replace(/-/g,'').length !== 8) {
            throw new Error(('is not a date'));
        }
        try{
            // @ts-ignore
            const d = new Date(value);
            if (d instanceof Date && !isNaN(d.getTime())) {
                return d;
            }
            throw new Error(('is not a datetime'));
        } catch(e) {
            throw new Error(('is not a date'));
        }
    }
    if (field && field.type && field.type === 'datetime') {
        try{
            // @ts-ignore
            const d = new Date(value);
            if(d instanceof Date && !isNaN(d.getTime())){
                return d;
            }
            throw new Error(('is not a datetime'));
        } catch(e) {
            throw new Error(('is not a datetime'));
        }
    }
    if (field && field.type && field.type === 'time') {
        try{
            // @ts-ignore
            let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
            if (a === null || (a[1]>24 || a[3]>60 || a[4]>60)) {
                throw new Error(('is not a time'));
            }
            return value
        } catch(e) {
            throw new Error(('is not a time'));
        }
    }
    if (field && field.type && field.type === 'boolean') {
        if (value === true || value === 'true' || value === 1 || value === '1') {
            return true;
        }
        if (value === false || value === 'false' || value === 0 || value === '0') {
            return false;
        }
        throw new Error(('is not a Boolean'));
        }
      return value;
    }
    <% for(let parameter in val.parameters) { %>
        // @ts-ignore
        let tapdata_<%-val.parameters[parameter].name%> = requestData ? requestData['<%-val.parameters[parameter].name%>'] : undefined;
        <% if (val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== "") {%>
            if (tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number') {
                // @ts-ignore
                tapdata_<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
            } else if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined) {
                // @ts-ignore
                tapdata_<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
            }
        <%}%>
        // @ts-ignore
        params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},tapdata_<%-val.parameters[parameter].name%>);
    <% } %>
    // tslint:disable-next-line:no-any
    let filter:any = {where:null,sort:[]};
    // tslint:disable-next-line:no-any
    let where:any = <%-val.whereString%>;
    <%_ if (val['fullCustomQuery']) { -%>
    filter.where = deepReplaceInPlace(JSON.stringify(where), params);
    <%_ } -%>
    <%_ if (!val['fullCustomQuery']) { -%>
    for (let x in where) {
        // @ts-ignore
        if (params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined) {
            // @ts-ignore
            let nWhere = {};
            switch(where[x].operator) {
                case '==':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = params[where[x].parameter]
                    break;
                case '>':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
                    break;
                case '>=':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
                    break;
                case '<':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
                    break;
                case '<=':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
                    break;
                case '!=':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
                    break;
                case 'like':
                    // @ts-ignore
                    nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
                    break;
            }
            // @ts-ignore
            if (!filter.where) {
                filter.where = nWhere
            } else if (where[x].condition === 'or') {
                filter.where = {'or':[filter.where,nWhere]}
            } else {
                filter.where = {'and':[filter.where,nWhere]}
            }
            //filter.where[where[x]['fieldName']] = params[where[x].parameter]
        }
    }
    <%_ } -%>
    filter.order = <%-val.sortString%>;
    <% if (val.select && val.select.length>0) {%>
    filter.fields = <%-val.selectString%>;
    <%}%>
    let tapdataLimit = requestData ? requestData['limit'] : undefined;
    let tapdataPage = requestData ? requestData['page'] : undefined;
    filter.limit = tapdataLimit || 100;
    if (tapdataLimit && tapdataPage) {
        filter.skip = tapdataLimit * (tapdataPage -1);
    }
    if (filter.order.length === 0) {
        delete filter.order
    }
    if (filter.where === null) {
        delete filter.where
    }
    let whereIs = {};
    if (filter['where']) {
        whereIs = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = whereIs || filter['where'] || {};
    let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (total <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, total};
    }
    let data = await this.<%= repositoryNameCamel %>.find(filter);
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
    let dataInfo = handleNameMappingAll(data, aliasNameMapping);
    //@ts-ignore
    return {dataInfo, total};
}

//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@get('<%= val['path'] %>/{id}', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '200': {
      description: '<%= modelName %> model instance',
      content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.get"
})
async findById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<<%= modelName %>> {
  return await this.<%= repositoryNameCamel %>.findById(id);
}

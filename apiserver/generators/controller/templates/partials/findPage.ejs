//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
    })
    @get('<%= val['path'] %>/count', {
        summary: 'count data',
        tags:['<%= name %>'],
        responses: {
            '200': {
                description: 'Object of count data, result.total is model count.',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                                total: { type: 'object', properties: { count: { type: 'number' } } }
                            }
                    },
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async count(
@param.query.object('filter', getWhereSchemaFor(<%= modelName %>)) filter?: any
): Promise<{total: Count}> {
    filter = filter || {};
    let total = await this.<%= repositoryNameCamel %>.count(filter);
    return {
        total: total
    };
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@get('<%= val['path'] %><%= val['getSuffix'] %>', {
    summary: '<%= val['summary']%>',
    tags:['<%= name %>'],
    responses: {
        '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }
                    },
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async findPage(
    @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
    @param.query.string('filename') filename?: String,
    @param.query.string('page') userpage?: String,
    @param.query.string('limit') userlimit?: String,
): Promise<{data: <%= modelName %>[], total:Count}> {
    filter = filterFields(filter || {}, (globalFields) => {
        <%_ if (val['fields']) { -%>
        Object.assign(globalFields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>
    });
    if (!filename) {
        const limit = Number(filter.limit);
        if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
            filter.limit = config.get('defaultLimit') || defaultPageLimit;
        }
    }
    if ( userlimit && userlimit !== '') {
        filter.limit = Number(userlimit)
        if (filter.limit <= 0) {
            filter.limit = config.get('defaultLimit') || defaultPageLimit;
        }
    }
    const limitInConfig = config.get('maxLimit') || 0;
    if (limitInConfig > 0) {
        filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
    }
    if ( userpage && userpage !== '') {
        // @ts-ignore
        filter.skip = filter.limit * (Number(userpage) - 1);
    }
    const rawWhere = (filter.where || {});
    let requires: string[] = [] ;
    let available: string[] = [] ;
    <%_ if (val['requiredQueryField']) { -%>
    requires = <%- JSON.stringify(val['requiredQueryField']) %>;
    <%_ } -%>
    <%_ if (val['availableQueryField']) { -%>
    available = <%- JSON.stringify(val['availableQueryField']) %>;
    <%_ } -%>
    let allField: string[] = [];
    this.findAllField(rawWhere, allField);
    if (requires.length > 0) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
        let ok = false;
        for (let index = 0; index < requires.length; index++) {
            const er = requires[index];
            ok = allField.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    if (available.length > 0) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
        let ok = true;
        for (let index = 0; index < allField.length; index++) {
            const er = allField[index];
            ok = available.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    let where = {};
    if (filter['where']) {
        where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = where || filter['where'] || {};
    let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (total <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, total};
    }
    let data = await this.<%= repositoryNameCamel %>.find(filter);
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
    let dataInfo = handleNameMappingAll(data, aliasNameMapping);
    //@ts-ignore
    return {dataInfo, total};
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@post('<%= val['path'] %><%= val['postSuffix'] %>', {
    summary: '<%= val['summary']%>',
    tags:['<%= name %>'],
    responses: {
        '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }
                    },
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async findPage_post( @requestBody() filter?: Filter<<%= modelName %>>,): Promise<{data: <%= modelName %>[], total:Count}> {
    filter = filterFields(filter || {}, (globalFields) => {
        <%_ if (val['fields']) { -%>
            Object.assign(globalFields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>
    });
    const limit = Number(filter.limit);
    if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
        filter.limit = config.get('defaultLimit') || defaultPageLimit;
    }
    const limitInConfig = config.get('maxLimit') || 0;
    if (limitInConfig > 0) {
        filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
    }
    const rawWhere = (filter.where || {});
    let requires: string[] = [] ;
    let available: string[] = [] ;
    <%_ if (val['requiredQueryField']) { -%>
    requires = <%- JSON.stringify(val['requiredQueryField']) %>;
    <%_ } -%>
    <%_ if (val['availableQueryField']) { -%>
    available = <%- JSON.stringify(val['availableQueryField']) %>;
    <%_ } -%>
    let allField: string[] = [];
    this.findAllField(rawWhere, allField);
    if (requires.length > 0) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
        let ok = false;
        for (let index = 0; index < requires.length; index++) {
            const er = requires[index];
            ok = allField.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    // @ts-ignore
    if(filter && filter.page && filter.limit){
        // @ts-ignore
        filter.skip = (filter.page - 1) * filter.limit;
    }
    if ( available.length > 0 ) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
        let ok = true;
        for (let index = 0; index < allField.length; index++) {
            const er = allField[index];
            ok = available.includes(er);
            if (!ok) { break }
        }
        if (!ok) {
            throw err;
        }
    }

    let where = {};
    if (filter['where']) {
        where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = where || filter['where'] || {};
    let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (total <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, total};
    }
    let data = await this.<%= repositoryNameCamel %>.find(filter);
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
    let dataInfo = handleNameMappingAll(data, aliasNameMapping);
    //@ts-ignore
    return {dataInfo, total};
}

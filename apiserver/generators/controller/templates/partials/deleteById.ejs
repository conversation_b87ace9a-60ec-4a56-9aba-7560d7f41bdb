//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@del('<%= val['path'] %>/{id}', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '204': {
      description: '<%= modelName %> DELETE success',
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.delete"
})
async deleteById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
  await this.<%= repositoryNameCamel %>.deleteById(id);
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>/delete',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@get('<%= val['path'] %>/{id}/delete', {
    summary: '<%= val['summary']%>',
    tags:['<%= name %>'],
    responses: {
        '204': {
            description: '<%= modelName %> DELETE success',
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>"
})
async deleteById_get(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
    await this.<%= repositoryNameCamel %>.deleteById(id);
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'deleteWithWhere',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@del('<%= val['path'] %>', {
	summary: 'Delete all match document with where',
    tags:['<%= name %>'],
	responses: {
		'204': {
			description: '<%= modelName %> DELETE success',
		},
	},
	"x-table-name": "<%=tableName%>",
	"x-api-id": "<%=apiId%>",
	"x-api-name": "<%=apiName%>",
	"x-bsm-operation": "<%= modelName %>.deleteWithWhere"
})
async deleteWithWhere(
            @param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
) {
    await this.<%= repositoryNameCamel %>.deleteAll(where);
}
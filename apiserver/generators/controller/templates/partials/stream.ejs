//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@get('<%= val['path'] %>', {
    summary: '<%= val['summary']%>',
    tags:['<%= name %>'],
    responses: {
        '200': {
            description: 'Object of page data, result.data is model instances, result.total is model count.',
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }
                    },
                },
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
})
async <%= val['name'] %>(): Promise<object> {
    return {
        "database": "<%=dataSource.settings.database%>",
        "url": "<%=dataSource.settings.url%>",
        "ssl": <%=dataSource.settings.ssl%>,
        "sslCA": "<%=dataSource.settings.sslCA%>",
        <%_ if (val['filter']) { -%>
            "filter":this.<%= repositoryNameCamel %>.dataSource.adapter.buildWhere('<%= tableName %>',<%- JSON.stringify(val['filter']) %>),
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            "fields":<%- JSON.stringify(val['fields']) %>,
        <%_ } -%>
        "collection":"<%=tableName%>"
    };
}

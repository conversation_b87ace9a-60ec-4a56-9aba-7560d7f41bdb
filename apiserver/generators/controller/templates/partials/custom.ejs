//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@get('<%= val['path'] %>', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '200': {
      description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
      content: {
        'application/json': {
          schema: {type: 'object', properties: {
            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
            total: { type: 'object', properties: { count: { type: 'number' } } }
          }},
        },
      },
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.list"
})
async <%= val['name'] %>(
  @param.query.object('filter', getFilterSchemaFor(<%= modelName %>),ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'GET')) filter?: Filter<<%= modelName %>>,
  @param.query.string('filename') filename?: String,
): Promise<{data: <%= modelName %>[], total:Count}> {
    filter = filter || {};
    if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
      Object.keys(filter.fields).forEach(key => {
          // @ts-ignore
          filter.fields[key] = filter.fields[key] !== "false" && filter.fields[key] !== false
      })
    }

    if( !filename ){
        const limit = Number(filter.limit);
        if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
            filter.limit = config.get('defaultLimit') || defaultPageLimit;
        }
    }
    const limitInConfig = config.get('maxLimit') || 0;
    if (limitInConfig > 0) {
        filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
    }
    const rawWhere = (filter.where || {});
    let requires: string[] = [] ;
    let available: string[] = [] ;
    <%_ if (val['requiredQueryField']) { -%>
      requires = <%- JSON.stringify(val['requiredQueryField']) %>;
    <%_ } -%>
    <%_ if (val['availableQueryField']) { -%>
      available = <%- JSON.stringify(val['availableQueryField']) %>;
    <%_ } -%>
    let allField: string[] = [];
    this.findAllField(rawWhere, allField);
    if (requires.length > 0) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
        let ok = false;
        for (let index = 0; index < requires.length; index++) {
            const er = requires[index];
            ok = allField.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    if( available.length > 0 ){
        const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
        let ok = true;
        for (let index = 0; index < allField.length; index++) {
            const er = allField[index];
            ok = available.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    <%_ if (val['filter']) { -%>
        filter.where = filter.where || {};
        Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
    <%_ } -%>
    <%_ if (val['fields']) { -%>
        filter.fields = filter.fields || {};
        Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
    <%_ } -%>
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
    let where = {};
    if(filter['where']){
        where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = where || filter['where'] || {};
    let resultTotal = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (resultTotal <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, resultTotal};
    }
    //@ts-ignore
    let dataInfo = await this.<%= repositoryNameCamel %>.find(filter)
    let resultData = handleNameMappingAll(dataInfo, aliasNameMapping);
    //@ts-ignore
    return {data: resultData, total: resultTotal};
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
      originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@post('<%= val['path'] %>', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '200': {
      description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
      content: {
        'application/json': {
          schema: {type: 'object', properties: {
            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
            total: { type: 'object', properties: { count: { type: 'number' } } }
          }},
        },
      },
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>_post.list"
})
async <%= val['name'] %>_post(
  @requestBody(ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'POST')) filter?: Filter<<%= modelName %>>,
  @param.query.string('filename') filename?: String,
): Promise<{data: <%= modelName %>[], total:Count}> {
    filter = filter || {};
    if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
        Object.keys(filter.fields).forEach(key => {
            // @ts-ignore
            filter.fields[key] = filter.fields[key] !== "false" && filter.fields[key] !== false
        })
    }
    if( !filename ){
        const limit = Number(filter.limit);
        if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
            filter.limit = config.get('defaultLimit') || defaultPageLimit;
        }
    }
    const limitInConfig = config.get('maxLimit') || 0;
    if (limitInConfig > 0) {
        filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
    }
    const rawWhere = (filter.where || {});
    let requires: string[] = [] ;
    let available: string[] = [] ;
    <%_ if (val['requiredQueryField']) { -%>
      requires = <%- JSON.stringify(val['requiredQueryField']) %>;
    <%_ } -%>
    <%_ if (val['availableQueryField']) { -%>
      available = <%- JSON.stringify(val['availableQueryField']) %>;
    <%_ } -%>
    let allField: string[] = [];
    this.findAllField(rawWhere, allField);
    if (requires.length > 0) {
        const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
        let ok = false;
        for (let index = 0; index < requires.length; index++) {
            const er = requires[index];
            ok = allField.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    if( available.length > 0 ){
        const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
        let ok = true;
        for (let index = 0; index < allField.length; index++) {
            const er = allField[index];
            ok = available.includes(er);
            if (!ok) {
                break
            }
        }
        if (!ok) {
            throw err;
        }
    }
    <%_ if (val['filter']) { -%>
        filter.where = filter.where || {};
        Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
    <%_ } -%>
    <%_ if (val['fields']) { -%>
        filter.fields = filter.fields || {};
        Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
    <%_ } -%>
    let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;

    let where = {};
    if(filter['where']){
        where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
    }
    filter['where'] = where || filter['where'] || {};
    let resultTotal = await this.<%= repositoryNameCamel %>.count(filter['where']);
    //@ts-ignore
    if (resultTotal <= 0) {
        let empty: Record<string, any> = [];
        //@ts-ignore
        return {empty, resultTotal};
    }
    //@ts-ignore
    let dataInfo = await this.<%= repositoryNameCamel %>.find(filter)
    let resultData = handleNameMappingAll(dataInfo, aliasNameMapping);
    //@ts-ignore
    return {data: resultData, total: resultTotal};
}

//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@patch('<%= val['path'] %>/{id}', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '204': {
      description: '<%= modelName %> PATCH success',
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.update"
})
async updateById(
  @param.path.<%= idType %>('id') id: <%= idType %>,
  @requestBody() <%= modelVariableName %>: <%= modelName %>,
): Promise<void> {
  await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
}

@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@post('<%= val['path'] %>/{id}', {
    summary: '<%= val['summary']%>',
    tags:['<%= name %>'],
    responses: {
        '204': {
        description: '<%= modelName %> PATCH success',
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>"
})
async updateById_post(
    @param.path.<%= idType %>('id') id: <%= idType %>,
    @requestBody() <%= modelVariableName %>: <%= modelName %>,
    ): Promise<void> {
    await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
}



@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@patch('<%= val['path'] %>', {
    summary: 'update all match document with where',
    tags:['<%= name %>'],
    responses: {
        '204': {
            description: '<%= modelName %> PATCH success',
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.updateAll"
})
async updateAll(
@requestBody() <%= modelVariableName %>: <%= modelName %>,
@param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
): Promise<void> {
    await this.<%= repositoryNameCamel %>.updateAll(<%= modelVariableName %>, where);
}
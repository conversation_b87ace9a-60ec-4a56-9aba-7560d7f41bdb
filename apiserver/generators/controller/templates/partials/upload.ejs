//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'preset',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
})
@post('/api/v1/files/upload', {
    summary: '<%= val['summary'] %>',
    tags:['<%= name %>'],
    responses: {
        '200': {
            content: {
                'application/json': {
                    schema: {
                            type: 'object',
                            properties: {
                                total: { type: 'number' },
                                details: { type: 'object'}
                            }
                        },
                    }
            },
        },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.upload"
})
async upload(
    @requestBody({
        description: 'multipart/form-data value.',
        required: true,
        content: {
            'multipart/form-data': {
                'x-parser': 'stream',
                schema: {type: 'object'},
            },
        },
    })
    request: Request,
    @inject(RestBindings.Http.CONTEXT)
    ctx: RequestContext): Promise<{total: 0, ids?: {}}> {
        let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');
        const multer = require('multer');
        const upload = multer({
            dest: tmpdir,
            limits: {
                fileSize: 1024*1024*100
            }
        });
        return new Promise<{total: 0}>((resolve, reject) => {
            upload.any()(request, ctx.response, (err: any) => {
            if ( err ) {
                reject(err);
            } else {
                // @ts-ignore
                let files: any[] = request.files;
                let count: number = files.length;
                let promises: Promise<any>[] = [];
                files.forEach(( file => {
                    promises.push(this.<%= repositoryNameCamel %>.upload(file));
                }));
                Promise.all(promises).then((results => {
                    // @ts-ignore
                    resolve({ count: count, ids: results })
                })).catch(reject);
            }
        });
    });
}
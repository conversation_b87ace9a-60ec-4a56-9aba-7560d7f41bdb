//!!!代码不是我写的，纯复制过来做格式化的，提高代码可读性
@authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>',
    result:'<%= val['result'] %>',
    description:'<%= val['description'] %>',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
    originalConfig: <%- JSON.stringify(originalConfig)%>
  })
@get('<%= val['path'] %>/{id}', {
  summary: '<%= val['summary']%>',
  tags:['<%= name %>'],
  responses: {
    '200': {
      description: 'file stream',
      content: {'application/octet-stream ': {}},
    },
    '404': {
        description: 'file not found'
    },
  },
  "x-table-name": "<%=tableName%>",
  "x-api-id": "<%=apiId%>",
  "x-api-name": "<%=apiName%>",
  "x-bsm-operation": "<%= modelName %>.downloadById",
})
async downloadById(@param.path.<%= idType %>('id') id: <%= idType %>,@inject(RestBindings.Http.RESPONSE) res: Response,): Promise<any> {
    // @ts-ignore
    res.__fun ="download";
    let file : any = await this.<%= repositoryNameCamel %>.findById(id);
    if (!file) {
        throw new HttpErrors.NotFound("File not found");
    }
    let filename = file.filename ? file.filename.split('/') : ['file'];
    filename = filename[filename.length - 1]
    if ( file.metadata && file.metadata.file_name) {
        filename = file.metadata.file_name
    }
    if ( file.metadata && file.metadata.file_extension) {
        filename = `${filename}.${file.metadata.file_extension}`
    }
    return {
        filename: encodeURIComponent(filename),
        stream: await this.<%= repositoryNameCamel %>.downloadById(id)
    };
}

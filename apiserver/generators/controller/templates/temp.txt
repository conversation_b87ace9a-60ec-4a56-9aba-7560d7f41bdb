import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getFilterSchemaFor,
  getWhereSchemaFor,
  patch,
  del,
  requestBody,
  HttpErrors,
  RestBindings,
  RequestContext, Request,Response
} from '@loopback/rest';
import {<%= modelName %>} from '../models';
import {<%= repositoryName %>} from '../repositories';
import {
  AuthenticationBindings,
  UserProfile,
  authenticate,
} from '@loopback/authentication';
import {inject,intercept,InvocationContext,ValueOrPromise,InvocationResult,Interceptor} from '@loopback/context';
import {importExcel} from '../importExcel';
const Conf = require('conf');
const config = new Conf();
const defaultPageLimit = 20;
const log: Interceptor = async (invocationCtx:any, next:any) => {
const recv_timestmap = new Date().getTime();
let result = await next();
if (Array.isArray(result)) {
} else {
const resp_timestmap = new Date().getTime();
if(!result)
result={};
result.api_monit_info = {};
result.api_monit_info.recv_timestmap = recv_timestmap;
result.api_monit_info.db_request_exhaust = resp_timestmap - recv_timestmap;
result.api_monit_info.resp_timestmap = resp_timestmap;
if(result.data){
result.data = js_traverse(result.data);
} else if(invocationCtx.methodName.indexOf('download')<0) {
result = js_traverse(result);
}
}
return result;
};

@intercept(log)
export class <%= className %>Controller {
  constructor(
    @repository(<%= repositoryName %>)
    public <%= repositoryNameCamel %> : <%= repositoryName %>,
  ) {}
/*
async intercept(
invocationCtx: InvocationContext,
next: () => ValueOrPromise<InvocationResult>,
    ) {
    const recv_timestmap = new Date().getTime();
    const result = await next();
    if (Array.isArray(result)) {
    } else {
    const resp_timestmap = new Date().getTime();
    result.api_monit_info = {};
    result.api_monit_info.recv_timestmap = resp_timestmap - recv_timestmap;
    result.api_monit_info.resp_timestmap = resp_timestmap;
    if(result.data){
    result.data = js_traverse(result.data);
    }
    }
    return result;
    }
*/
  <%_ Object.entries(api).forEach(([path, val]) => { -%>
    <%_ if (val['type'] === 'preset') { -%>

      <%_ if (val['name'] === 'create') { -%>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @post('<%= val['path'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: '<%= modelName %> model instance',
              content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.create"
        })
        async create(@requestBody() <%= modelVariableName %>: <%= modelName %>): Promise<<%= modelName %>> {
          return await this.<%= repositoryNameCamel %>.create(<%= modelVariableName %>);
        }


        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'getBatchTemplate',
            result:'Document',
            description:'Download batch import excel template',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>/batch',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @get('<%= val['pathTpl'] %>/batch', {
            summary: 'Download batch import excel template',
            tags:['<%= name %>'],
            responses: {
                '200': {
                    description: 'Download batch import excel template',
                    content: {'application/octet-stream': {}},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.getBatchTemplate"
        })
        async getBatchTemplate(@inject(RestBindings.Http.CONTEXT) ctx: RequestContext): Promise<object> {

            let properties = this.<%= repositoryNameCamel %>.entityClass.definition.properties;
            let modelName = this.<%= repositoryNameCamel %>.entityClass.definition.name;
            let fields = Object.keys(properties).map(function(name){
            // @ts-ignore
            let title = properties[name].jsonSchema && properties[name].jsonSchema.title || '';
                return title ? `${title}(${name})` : name;
            });

            ctx.response.setHeader('Content-Disposition', 'attachment; filename="' + modelName + '.xlsx"');

            return importExcel.generatorTemplate([fields], `${modelName} records`);
        }




        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'getBatchTemplate',
            result:'Document',
            description:'Batch import excel records',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>/batch',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @post('<%= val['pathTpl'] %>/batch', {
            summary: 'Batch import excel records',
            tags:['<%= name %>'],
            responses: {
            '200': {
            description: 'Batch import excel records',
                content: {'application/json': {
                    schema: {type: 'object', properties: {
                        total: { type: 'number' },
                            details: { type: 'object'}
                        }},
                    }},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.batchImport"
        })
        async batchImport(
            @requestBody({
                description: 'multipart/form-data value.',
                required: true,
                content: {
                    'multipart/form-data': {
                        'x-parser': 'stream',
                        schema: {type: 'object'},
                    },
                },
            })
            request: Request,
        @inject(RestBindings.Http.CONTEXT)
        ctx: RequestContext): Promise<{total: 0, details?: {}}> {
            let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');

            const multer = require('multer');
            const upload = multer({
                dest: tmpdir,
                limits: {
                    fileSize: 1024*1024*100
                },
                fileFilter: (req: any, file: any, cb: any) => {
                    let originalName = file.originalname || '';
                    if( originalName.lastIndexOf('.xlsx') >= 0 ) {
                        cb(null, true);
                    } else {
                        cb(null, false);
                    }
                }
            });

            return new Promise<{total: 0}>((resolve, reject) => {

                upload.any()(request, ctx.response, (err: any) => {
                if( err ){
                    reject(err);
                } else {
                    // @ts-ignore
                    let files: any[] = request.files;

                    let filePaths = files.map( file => file.path);

                    importExcel.readFromFiles(filePaths).then((result) => {

                        let total: number = 0;
                        let promises: Promise<<%= modelName %>>[] = [];
                        Object.keys(result).forEach(file => {

                        // @ts-ignore
                        Object.keys(result[file]).forEach(sheet => {

                            // @ts-ignore
                            total += result[file][sheet].length;

                            // @ts-ignore
                            promises.push(this.<%= repositoryNameCamel %>.createAll(result[file][sheet]));
                        });
                    });

                    Promise.all(promises).then((results => {

                        // @ts-ignore
                        resolve({ total: total })

                    })).catch(reject);

                    }).catch(reject);
                }
            });
        });
    }





    <%_ } else if (val['name'] === 'findPage') { %>

            @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
            })
            @get('<%= val['path'] %>/count', {
            summary: 'count data',
            tags:['<%= name %>'],
            responses: {
            '200': {
            description: 'Object of count data, result.total is model count.',
            content: {
            'application/json': {
            schema: {type: 'object', properties: {
            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
            total: { type: 'object', properties: { count: { type: 'number' } } }
            }},
            },
            },
            },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.list"
            })
            async count(
            @param.query.object('filter', getWhereSchemaFor(<%= modelName %>)) filter?: any
            ): Promise<{total: Count}> {
            filter = filter || {};


            let total = await this.<%= repositoryNameCamel %>.count(filter);
            return {
            total: total
            };
            }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @get('<%= val['path'] %><%= val['getSuffix'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
              content: {
                'application/json': {
                  schema: {type: 'object', properties: {
                    data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                    total: { type: 'object', properties: { count: { type: 'number' } } }
                  }},
                },
              },
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.list"
        })
        async findPage(
          @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
          @param.query.string('filename') filename?: String,
            @param.query.string('page') userpage?: String,
            @param.query.string('limit') userlimit?: String,
        ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filterFields(filter || {}, (globalFields) => {
            <%_ if (val['fields']) { -%>
                Object.assign(globalFields, <%- JSON.stringify(val['fields']) %>);
            <%_ } -%>
            });
          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
                filter.limit = config.get('defaultLimit') || defaultPageLimit;
            }
          }
            if( userlimit && userlimit !== ''){
                filter.limit = Number(userlimit)
                if (filter.limit <= 0) {
                    filter.limit = config.get('defaultLimit') || defaultPageLimit;
                }
            }

            const limitInConfig = config.get('maxLimit') || 0;
            if (limitInConfig > 0) {
                filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
            }
            if( userpage && userpage !== ''){
            // @ts-ignore
            filter.skip = filter.limit * (Number(userpage) - 1);
            }
          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }
            let where = {};
            if(filter['where']){
                where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
            }
            filter['where'] = where || filter['where'] || {};
            let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
            //@ts-ignore
            if (total <= 0) {
                let empty: Record<string, any> = [];
                //@ts-ignore
                return {empty, total};
            }
            let data = await this.<%= repositoryNameCamel %>.find(filter);
            let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
            let dataInfo = handleNameMappingAll(data, aliasNameMapping);
            //@ts-ignore
            return {dataInfo, total};
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @post('<%= val['path'] %><%= val['postSuffix'] %>', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '200': {
                    description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
                    content: {
                        'application/json': {
                            schema: {type: 'object', properties: {
                            data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                            total: { type: 'object', properties: { count: { type: 'number' } } }
                        }},
                    },
                },
            },
        },
        "x-table-name": "<%=tableName%>",
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async findPage_post( @requestBody() filter?: Filter<<%= modelName %>>,
            ): Promise<{data: <%= modelName %>[], total:Count}> {
            filter = filterFields(filter || {}, (globalFields) => {
                <%_ if (val['fields']) { -%>
                    Object.assign(globalFields, <%- JSON.stringify(val['fields']) %>);
                <%_ } -%>
            });
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
                filter.limit = config.get('defaultLimit') || defaultPageLimit;
            }
            const limitInConfig = config.get('maxLimit') || 0;
            if (limitInConfig > 0) {
                filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
            }
            const rawWhere = (filter.where || {});
            let requires: string[] = [] ;
            let available: string[] = [] ;

            <%_ if (val['requiredQueryField']) { -%>
                requires = <%- JSON.stringify(val['requiredQueryField']) %>;
            <%_ } -%>

            <%_ if (val['availableQueryField']) { -%>
                available = <%- JSON.stringify(val['availableQueryField']) %>;
            <%_ } -%>

            let allField: string[] = [];
            this.findAllField(rawWhere, allField);

            if (requires.length > 0) {
                const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
                let ok = false;
                for (let index = 0; index < requires.length; index++) {
                    const er = requires[index];
                    ok = allField.includes(er);
                    if (!ok) { break }
                }
                if (!ok) {
                    throw err;
                }
            }
            // @ts-ignore
            if(filter && filter.page && filter.limit){
            // @ts-ignore
            filter.skip = (filter.page - 1) * filter.limit;
            }
            if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
                for (let index = 0; index < allField.length; index++) {
                    const er = allField[index];
                    ok = available.includes(er);
                    if (!ok) { break }
                }
                if (!ok) {
                    throw err;
                }
            }

            let where = {};
            if(filter['where']){
                where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
            }
            filter['where'] = where || filter['where'] || {};
            let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
            //@ts-ignore
            if (total <= 0) {
                let empty: Record<string, any> = [];
                //@ts-ignore
                return {empty, total};
            }
            let data = await this.<%= repositoryNameCamel %>.find(filter);
            let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
            let dataInfo = handleNameMappingAll(data, aliasNameMapping);
            //@ts-ignore
            return {dataInfo, total};
        }

      <%_ } else if (val['name'] === 'findById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @get('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: '<%= modelName %> model instance',
              content: {'application/json': {schema: {'x-ts-type': <%= modelName %>}}},
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.get"
        })
        async findById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<<%= modelName %>> {
          return await this.<%= repositoryNameCamel %>.findById(id);
        }
      <%_ } else if (val['name'] === 'updateById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @patch('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '204': {
              description: '<%= modelName %> PATCH success',
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.update"
        })
        async updateById(
          @param.path.<%= idType %>('id') id: <%= idType %>,
          @requestBody() <%= modelVariableName %>: <%= modelName %>,
        ): Promise<void> {
          await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @post('<%= val['path'] %>/{id}', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '204': {
                description: '<%= modelName %> PATCH success',
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>"
        })
        async updateById_post(
            @param.path.<%= idType %>('id') id: <%= idType %>,
            @requestBody() <%= modelVariableName %>: <%= modelName %>,
            ): Promise<void> {
            await this.<%= repositoryNameCamel %>.updateById(id, <%= modelVariableName %>);
        }



        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @patch('<%= val['path'] %>', {
            summary: 'update all match document with where',
            tags:['<%= name %>'],
            responses: {
            '204': {
            description: '<%= modelName %> PATCH success',
            },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.updateAll"
        })
        async updateAll(
        @requestBody() <%= modelVariableName %>: <%= modelName %>,
        @param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
        ): Promise<void> {
            await this.<%= repositoryNameCamel %>.updateAll(<%= modelVariableName %>, where);
         }
      <%_ } else if (val['name'] === 'deleteById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @del('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '204': {
              description: '<%= modelName %> DELETE success',
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.delete"
        })
        async deleteById(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
          await this.<%= repositoryNameCamel %>.deleteById(id);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>/delete',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @get('<%= val['path'] %>/{id}/delete', {
            summary: '<%= val['summary']%>',
            tags:['<%= name %>'],
            responses: {
                '204': {
                    description: '<%= modelName %> DELETE success',
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>"
        })
        async deleteById_get(@param.path.<%= idType %>('id') id: <%= idType %>): Promise<void> {
            await this.<%= repositoryNameCamel %>.deleteById(id);
        }

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'deleteWithWhere',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
		@del('<%= val['path'] %>', {
			summary: 'Delete all match document with where',
            tags:['<%= name %>'],
			responses: {
				'204': {
					description: '<%= modelName %> DELETE success',
				},
			},
			"x-table-name": "<%=tableName%>",
			"x-api-id": "<%=apiId%>",
			"x-api-name": "<%=apiName%>",
			"x-bsm-operation": "<%= modelName %>.deleteWithWhere"
		})
        async deleteWithWhere(
                    @param.query.object('where', getWhereSchemaFor(<%= modelName %>)) where?: Where<<%= modelName %>>
        ) {
            await this.<%= repositoryNameCamel %>.deleteAll(where);
        }

      <%_ } else if (val['name'] === 'downloadById') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @get('<%= val['path'] %>/{id}', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
          responses: {
            '200': {
              description: 'file stream',
              content: {'application/octet-stream ': {}},
            },
            '404': {
                description: 'file not found'
            },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.downloadById",
        })
        async downloadById(@param.path.<%= idType %>('id') id: <%= idType %>,@inject(RestBindings.Http.RESPONSE) res: Response,): Promise<any> {
                                // @ts-ignore
                                res.__fun ="download";
          let file : any = await this.<%= repositoryNameCamel %>.findById(id);
          if(!file){
            throw new HttpErrors.NotFound("File not found");
          }
          let filename = file.filename ? file.filename.split('/') : ['file'];
          filename = filename[filename.length - 1]
          if( file.metadata && file.metadata.file_name){
            filename = file.metadata.file_name
          }
          if( file.metadata && file.metadata.file_extension){
            filename = `${filename}.${file.metadata.file_extension}`
          }
          return {
            filename: encodeURIComponent(filename),
            stream: await this.<%= repositoryNameCamel %>.downloadById(id)
          };
        }

      <%_ } else if (val['name'] === 'download') { %>

        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
          })
        @get('<%= val['path'] %><%= val['getSuffix'] %>', {
          summary: '<%= val['summary']%>',
          tags:['<%= name %>'],
            responses: {
              '200': {
                  description: 'file stream',
                  content: {'application/octet-stream ': {}},
                },
                '404': {
                    description: 'file not found'
                },
          },
          "x-table-name": "<%=tableName%>",
          "x-api-id": "<%=apiId%>",
          "x-api-name": "<%=apiName%>",
          "x-bsm-operation": "<%= modelName %>.download"
        })
        async download(
          @inject(RestBindings.Http.RESPONSE) res: Response,
          @param.query.object('filter', getFilterSchemaFor(<%= modelName %>)) filter?: Filter<<%= modelName %>>,
        ): Promise<any> {
                                    // @ts-ignore
                                    res.__fun ="download";
          let files = await this.<%= repositoryNameCamel %>.find(filter);
          let file : any = files && files.length > 0 ? files[0] : null;
          if( file ){
            let filename = file.filename ? file.filename.split('/') : ['file'];
            filename = filename[filename.length - 1]
            if( file.metadata && file.metadata.file_name){
              filename = file.metadata.file_name
            }
            if( file.metadata && file.metadata.file_extension){
              filename = `${filename}.${file.metadata.file_extension}`
            }
            return {
              filename: encodeURIComponent(filename),
              stream: await this.<%= repositoryNameCamel %>.downloadById(file._id || '')
            };
          } else {
            throw new HttpErrors.NotFound("File not found");
          }
        }
      <%_ } else if (val['name'] === 'upload') { %>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'preset',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @post('/api/v1/files/upload', {
            summary: '<%= val['summary'] %>',
            tags:['<%= name %>'],
            responses: {
            '200': {
                content: {'application/json': {
                    schema: {type: 'object', properties: {
                        total: { type: 'number' },
                            details: { type: 'object'}
                        }},
                    }},
                },
            },
            "x-table-name": "<%=tableName%>",
            "x-api-id": "<%=apiId%>",
            "x-api-name": "<%=apiName%>",
            "x-bsm-operation": "<%= modelName %>.upload"
        })
        async upload(
            @requestBody({
                description: 'multipart/form-data value.',
                required: true,
                content: {
                    'multipart/form-data': {
                        'x-parser': 'stream',
                        schema: {type: 'object'},
                    },
                },
            })
            request: Request,
            @inject(RestBindings.Http.CONTEXT)
            ctx: RequestContext): Promise<{total: 0, ids?: {}}> {
                let tmpdir = require('path').join(require('os').tmpdir(), 'api-server');

                const multer = require('multer');
                const upload = multer({
                    dest: tmpdir,
                    limits: {
                        fileSize: 1024*1024*100
                    }
                });

                return new Promise<{total: 0}>((resolve, reject) => {

                    upload.any()(request, ctx.response, (err: any) => {
                    if( err ){
                        reject(err);
                    } else {
                        // @ts-ignore
                        let files: any[] = request.files;

                        let count: number = files.length;
                        let promises: Promise<any>[] = [];
                        files.forEach(( file => {
                            promises.push(this.<%= repositoryNameCamel %>.upload(file));
                        }));
                        Promise.all(promises).then((results => {

                            // @ts-ignore
                            resolve({ count: count, ids: results })

                        })).catch(reject);

                    }
                });
            });
        }
      <%_ }  %>
<%_ } else if (val['name'] === 'customerQuery') { %>

    @authenticate('JwtStrategy', {
    allPathId:'<%= val['allPathId'] %>',
    rawName:'<%= val['rawName'] %>customerQuery',
    result:'<%= val['result'] %>customerQuery',
    description:'<%= val['description'] %> customerQuery',
    type:'<%= val['type'] %>',
    pathTpl:'<%= val['pathTpl'] %>',
    roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
    })
    @get('<%= val['path'] %><%= val['getSuffix'] %>', {
    summary: 'Customer Query',
    tags:['<%= name %>'],
    responses: {
    '200': {
    description: 'Object of page data, result.data is model instances, result.total is model count.',
    content: {
    'application/json': {
    schema: {type: 'object', properties: {
    data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
    total: { type: 'object', properties: { count: { type: 'number' } } }
    }},
    },
    },
    },
    },
    "x-table-name": "<%=tableName%>",
    "x-api-id": "<%=apiId%>",
    "x-api-name": "<%=apiName%>",
    "x-bsm-operation": "<%= modelName %>.list"
    })
    async customerQuery(
      <% const paramTypeMapping = {date:'string',datetime:'string',time:'string'} %>
      <% for(let parameter in val.parameters) { %>
      <% const paramType = paramTypeMapping[val.parameters[parameter].type] || val.parameters[parameter].type %>
    @param.query.string('<%-val.parameters[parameter].name%>') tapdata_<%-val.parameters[parameter].name%> : <%-paramType%>,
      <% } %>
    ): Promise<{data: <%= modelName %>[], total:Count}>{
                                            let params = {};
                                            const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined){
                                              if(value === undefined || value === null){
                                                return value;
                                              }
                                              if(field && field.type && field.type === 'string'){
                                                return value.toString();
                                              }
                                              if(field && field.type && field.type === 'number'){
                                                // @ts-ignore
                                                const nValue = value * 1;
                                                if(nValue.toString() !== value.toString()){
                                            throw new Error(('is not a number'));
                                                }
                                                return nValue
                                              }
                                              if(field && field.type && field.type === 'date'){
                                            // @ts-ignore
                                            if(value.replace(/-/g,'').length !== 8){
                                            throw new Error(('is not a date'));
                                            }
                                                try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
                                                }catch(e){
                                            throw new Error(('is not a date'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'datetime'){
                                                try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
                                                }catch(e){
                                            throw new Error(('is not a datetime'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'time'){
                                                try{
                                            // @ts-ignore
                                            let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
                                            if(a === null || (a[1]>24 || a[3]>60 || a[4]>60)){
                                            throw new Error(('is not a time'));
                                            }
                                                  return value
                                                }catch(e){
                                            throw new Error(('is not a time'));
                                                }
                                              }
                                              if(field && field.type && field.type === 'boolean'){
                                                if(value === true || value === 'true' || value === 1 || value === '1'){
                                                  return true;
                                                }
                                                if(value === false || value === 'false' || value === 0 || value === '0'){
                                                  return false;
                                                }
                                            throw new Error(('is not a Boolean'));
                                              }
                                              return value;
                                            }
                                            <% for(let parameter in val.parameters) { %>
                                            // @ts-ignore
                                            <% if(val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== ""){%>
                                            if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number'){
                                            // @ts-ignore
                                            tapdata_<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
                                            }else if(tapdata_<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined){
                                            // @ts-ignore
                                                tapdata_<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
                                              }
                                             <%}%>
                                              // @ts-ignore
                                              params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},tapdata_<%-val.parameters[parameter].name%>);
                                            <% } %>
        // tslint:disable-next-line:no-any
        let filter:any = {where:null,sort:[]};
                                            // tslint:disable-next-line:no-any
                                            let where:any = <%-val.whereString%>;
                                            for(let x in where){
                                            // @ts-ignore
                                            if(params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined){
                                            // @ts-ignore
                                            let nWhere = {};
                                            if(where[x].operator === '=='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = params[where[x].parameter]
                                            } else if (where[x].operator === '>'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
                                            } else if (where[x].operator === '>='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
                                            } else if (where[x].operator === '<'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
                                            } else if (where[x].operator === '<='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
                                            } else if (where[x].operator === '!='){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
                                            } else if (where[x].operator === 'like'){
                                            // @ts-ignore
                                            nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
                                            }
                                            // @ts-ignore
                                            if(!filter.where){
                                              filter.where = nWhere
                                            }else if(where[x].condition === 'or'){
                                              filter.where = {'or':[filter.where,nWhere]}
                                            } else {
                                              filter.where = {'and':[filter.where,nWhere]}
                                            }
                                            //filter.where[where[x]['fieldName']] = params[where[x].parameter]
                                            }
                                            }
                                            filter.order = <%-val.sortString%>;
                                            <% if(val.select && val.select.length>0){%>
                                            filter.fields = <%-val.selectString%>;
                                            <%}%>
                                            filter.limit = tapdata_limit || 100;
                                            if(tapdata_limit && tapdata_page){
                                            filter.skip = tapdata_limit * (tapdata_page -1);
                                            }
                                            if(filter.order.length === 0){
                                            delete filter.order
                                            }
                                            if(filter.where === null){
                                            delete filter.where
                                            }
        let whereIs = {};
        if(filter['where']){
            whereIs = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
        }
        filter['where'] = whereIs || filter['where'] || {};
        let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
        //@ts-ignore
        if (total <= 0) {
            let empty: Record<string, any> = [];
            //@ts-ignore
            return {empty, total};
        }
        let data = await this.<%= repositoryNameCamel %>.find(filter);
        let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
        let dataInfo = handleNameMappingAll(data, aliasNameMapping);
        //@ts-ignore
        return {dataInfo, total};
        }


        @authenticate('JwtStrategy', {
        allPathId:'<%= val['allPathId'] %>',
        rawName:'<%= val['rawName'] %>customerQuery',
        result:'<%= val['result'] %>customerQuery',
        description:'<%= val['description'] %> customerQuery',
        type:'<%= val['type'] %>',
        pathTpl:'<%= val['pathTpl'] %>',
        roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @post('<%= val['path'] %><%= val['postSuffix'] %>', {
        summary: 'Customer Query',
        tags:['<%= name %>'],
        responses: {
        '200': {
        description: 'Object of page data, result.data is model instances, result.total is model count.',
        content: {
        'application/json': {
        schema: {type: 'object', properties: {
        data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
        total: { type: 'object', properties: { count: { type: 'number' } } }
        }},
        },
        },
        },
        },
        "x-table-name": "<%=tableName%>",
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async customerQuery_post(
                                            @requestBody() body?: any
        ): Promise<{data: <%= modelName %>[], total:Count}>{
        let params = {};
        const checkFields = function(field: { type: string; }, value: string | number | boolean | Date | null | undefined){
        if(value === undefined || value === null){
        return value;
        }
        if(field && field.type && field.type === 'string'){
        return value.toString();
        }
        if(field && field.type && field.type === 'number'){
        // @ts-ignore
        const nValue = value * 1;
        if(nValue.toString() !== value.toString()){
        throw new Error(('is not a number'));
        }
        return nValue
        }
        if(field && field.type && field.type === 'date'){
        // @ts-ignore
        if(value.replace(/-/g,'').length !== 8){
        throw new Error(('is not a date'));
        }
        try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
        }catch(e){
        throw new Error(('is not a date'));
        }
        }
        if(field && field.type && field.type === 'datetime'){
        try{
                                            // @ts-ignore
                                            const d = new Date(value);
                                            if(d instanceof Date && !isNaN(d.getTime())){
                                            return d;
                                            }
                                            throw new Error(('is not a datetime'));
        }catch(e){
        throw new Error(('is not a datetime'));
        }
        }
        if(field && field.type && field.type === 'time'){
        try{
        // @ts-ignore
        let a = value.match(/^(\d{1,2})(:)?(\d{1,2})\2(\d{1,2})$/);
        if(a === null || (a[1]>24 || a[3]>60 || a[4]>60)){
        throw new Error(('is not a time'));
        }
        return value
        }catch(e){
        throw new Error(('is not a time'));
        }
        }
        if(field && field.type && field.type === 'boolean'){
        if(value === true || value === 'true' || value === 1 || value === '1'){
        return true;
        }
        if(value === false || value === 'false' || value === 0 || value === '0'){
        return false;
        }
        throw new Error(('is not a Boolean'));
        }
        return value;
        }
        <% for(let parameter in val.parameters) { %>
            // @ts-ignore
            <% if(val.parameters[parameter].defaultvalue && val.parameters[parameter].defaultvalue !== ""){%>
            if(body.<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined && '<%-val.parameters[parameter].type%>' === 'number'){
            // @ts-ignore
                                                body.<%-val.parameters[parameter].name%> = <%-val.parameters[parameter].defaultvalue%>
            }else if(body.<%-val.parameters[parameter].name%> === undefined && '<%-val.parameters[parameter].defaultvalue%>' !== undefined){
            // @ts-ignore
                                                body.<%-val.parameters[parameter].name%> = '<%-val.parameters[parameter].defaultvalue%>'
            }
            <%}%>
            // @ts-ignore
            params['<%-val.parameters[parameter].name%>'] = checkFields({type:'<%-val.parameters[parameter].type%>'},body.<%-val.parameters[parameter].name%>);
        <% } %>
        // tslint:disable-next-line:no-any
        let filter:any = {where:null,sort:[]};
                                            // tslint:disable-next-line:no-any
        let where:any = <%-val.whereString%>;
        for(let x in where){
        // @ts-ignore
        if(params[where[x].parameter] !== "" && params[where[x].parameter] !== null && params[where[x].parameter] !== undefined){
        // @ts-ignore
        let nWhere = {};
        if(where[x].operator === '=='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = params[where[x].parameter]
        } else if (where[x].operator === '>'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {gt:params[where[x].parameter]}
        } else if (where[x].operator === '>='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {gte:params[where[x].parameter]}
        } else if (where[x].operator === '<'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {lt:params[where[x].parameter]}
        } else if (where[x].operator === '<='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {lte:params[where[x].parameter]}
        } else if (where[x].operator === '!='){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {ne:params[where[x].parameter]}
        } else if (where[x].operator === 'like'){
        // @ts-ignore
        nWhere[where[x]['fieldName']] = {like:params[where[x].parameter]}
        }
        // @ts-ignore
        if(!filter.where){
            filter.where = nWhere
        }else if(where[x].condition === 'or'){
        filter.where = {'or':[filter.where,nWhere]}
        } else {
        filter.where = {'and':[filter.where,nWhere]}
        }
        //filter.where[where[x]['fieldName']] = params[where[x].parameter]
        }
        }
        filter.order = <%-val.sortString%>;
        <% if(val.select && val.select.length>0){%>
        filter.fields = <%-val.selectString%>;
        <%}%>
        filter.limit = body.limit || filter.limit || config.get('defaultLimit') || defaultPageLimit;
        const page = body.page || filter.page || 1;
        const limitInConfig = config.get('maxLimit') || 0;
        if (limitInConfig > 0) {
            filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
        }
        if(filter.limit <= 0) {
            filter.limit = config.get('defaultLimit') || defaultPageLimit;
        }
        if(filter.limit && page){
            filter.skip = filter.limit * (page -1);
        }
        if(filter.order.length === 0){
          delete filter.order
        }
        if(filter.where === null){
          delete filter.where
        }
        let whereIs = {};
        if(filter['whereIs']){
            whereIs = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
        }
        filter['where'] = whereIs || filter['where'] || {};
        let total = await this.<%= repositoryNameCamel %>.count(filter['where']);
        //@ts-ignore
        if (total <= 0) {
            let empty: Record<string, any> = [];
            //@ts-ignore
            return {empty, total};
        }
        let data = await this.<%= repositoryNameCamel %>.find(filter);
        let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;
        let dataInfo = handleNameMappingAll(data, aliasNameMapping);
        //@ts-ignore
        return {dataInfo, total};
       }
    <%_ } else if (val['method'] === 'STREAM') { %>
        @authenticate('JwtStrategy', {
            allPathId:'<%= val['allPathId'] %>',
            rawName:'<%= val['rawName'] %>',
            result:'<%= val['result'] %>',
            description:'<%= val['description'] %>',
            type:'<%= val['type'] %>',
            pathTpl:'<%= val['pathTpl'] %>',
            roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
        @get('<%= val['path'] %><%= val['getSuffix'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
        '200': {
        description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
        content: {
        'application/json': {
        schema: {type: 'object', properties: {
        data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
        total: { type: 'object', properties: { count: { type: 'number' } } }
        }},
        },
        },
        },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
        })
        async <%= val['name'] %>(): Promise<{}> {
        return {
        "name": "rrr",
        "connector": "<%=dataSource.dataSourceType%>",
        "allowExtendedOperators": true,
        "useNewUrlParser": true,
        "url": "<%=dataSource.settings.url%>",
        "ssl": <%=dataSource.settings.ssl%>,
        "sslCA": "<%=dataSource.settings.sslCA%>",
        <%_ if (val['filter']) { -%>
            "filter":this.<%= repositoryNameCamel %>.dataSource.adapter.buildWhere('<%= tableName %>',<%- JSON.stringify(val['filter']) %>),
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            "fields":<%- JSON.stringify(val['fields']) %>,
        <%_ } -%>
        "collection":"<%=tableName%>"
        };
        }
    <%_ } else {-%>

      @authenticate('JwtStrategy', {
          allPathId:'<%= val['allPathId'] %>',
          rawName:'<%= val['rawName'] %>',
          result:'<%= val['result'] %>',
          description:'<%= val['description'] %>',
          type:'<%= val['type'] %>',
          pathTpl:'<%= val['pathTpl'] %>',
          roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
      @get('<%= val['path'] %><%= val['getSuffix'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
          '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
              'application/json': {
                schema: {type: 'object', properties: {
                  data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                  total: { type: 'object', properties: { count: { type: 'number' } } }
                }},
              },
            },
          },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>.list"
      })
      async <%= val['name'] %>(
        @param.query.object('filter', getFilterSchemaFor(<%= modelName %>),ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'GET')) filter?: Filter<<%= modelName %>>,
        @param.query.string('filename') filename?: String,
      ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    filter.fields[key] = filter.fields[key] !== "false" && filter.fields[key] !== false
                })
            }

          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
              filter.limit = config.get('defaultLimit') || defaultPageLimit;
            }
          }
            const limitInConfig = config.get('maxLimit') || 0;
            if (limitInConfig > 0) {
                filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
            }


          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

        <%_ if (val['filter']) { -%>
            filter.where = filter.where || {};
            Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            filter.fields = filter.fields || {};
            Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>
        let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;

        let where = {};
        if(filter['where']){
            where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
        }
        filter['where'] = where || filter['where'] || {};
        let resultTotal = await this.<%= repositoryNameCamel %>.count(filter['where']);
        //@ts-ignore
        if (resultTotal <= 0) {
            let empty: Record<string, any> = [];
            //@ts-ignore
            return {empty, resultTotal};
        }
        //@ts-ignore
        let dataInfo = await this.<%= repositoryNameCamel %>.find(filter)
        let resultData = handleNameMappingAll(dataInfo, aliasNameMapping);
        //@ts-ignore
        return {data: resultData, total: resultTotal};
      }

      @authenticate('JwtStrategy', {
          allPathId:'<%= val['allPathId'] %>',
          rawName:'<%= val['rawName'] %>',
          result:'<%= val['result'] %>',
          description:'<%= val['description'] %>',
          type:'<%= val['type'] %>',
          pathTpl:'<%= val['pathTpl'] %>',
          roles: <%- JSON.stringify(val['roles'])%>,
            originalConfig: <%- JSON.stringify(originalConfig)%>
        })
      @post('<%= val['path'] %>', {
        summary: '<%= val['summary']%>',
        tags:['<%= name %>'],
        responses: {
          '200': {
            description: 'Object of page data, result.data is <%= modelName %> model instances, result.total is model count.',
            content: {
              'application/json': {
                schema: {type: 'object', properties: {
                  data: {type: 'array', items: {'x-ts-type': <%= modelName %>}},
                  total: { type: 'object', properties: { count: { type: 'number' } } }
                }},
              },
            },
          },
        },
        "x-table-name": "<%=tableName%>",
        "x-fields": <%- JSON.stringify(val['fields'] || {}) %>,
        "x-api-id": "<%=apiId%>",
        "x-api-name": "<%=apiName%>",
        "x-bsm-operation": "<%= modelName %>_post.list"
      })
      async <%= val['name'] %>_post(
        @requestBody(ccc("<%= val['availableQueryFieldDescription'] %>","<%= val['requiredQueryFieldDescription'] %>",'POST')) filter?: Filter<<%= modelName %>>,
        @param.query.string('filename') filename?: String,
      ): Promise<{data: <%= modelName %>[], total:Count}> {
          filter = filter || {};

            if( typeof filter.fields === 'object' && Object.keys(filter.fields).length > 0){
                Object.keys(filter.fields).forEach(key => {
                    // @ts-ignore
                    filter.fields[key] = filter.fields[key] !== "false" && filter.fields[key] !== false
                })
            }

          if( !filename ){
            const limit = Number(filter.limit);
            if (isNaN(limit) || limit <= 0 || Math.ceil(limit) !== limit) {
              filter.limit = config.get('defaultLimit') || defaultPageLimit;
            }
          }
        const limitInConfig = config.get('maxLimit') || 0;
        if (limitInConfig > 0) {
            filter.limit = Number(filter.limit) > limitInConfig ? limitInConfig : Number(filter.limit);
        }


          const rawWhere = (filter.where || {});
          let requires: string[] = [] ;
          let available: string[] = [] ;

          <%_ if (val['requiredQueryField']) { -%>
            requires = <%- JSON.stringify(val['requiredQueryField']) %>;
          <%_ } -%>

          <%_ if (val['availableQueryField']) { -%>
            available = <%- JSON.stringify(val['availableQueryField']) %>;
          <%_ } -%>

          let allField: string[] = [];
          this.findAllField(rawWhere, allField);

          if (requires.length > 0) {
            const err = new HttpErrors.PreconditionFailed(`'where' conditions ${JSON.stringify(rawWhere)} must contain all fields of ${JSON.stringify(requires)}`);
            let ok = false;
            for (let index = 0; index < requires.length; index++) {
              const er = requires[index];
              ok = allField.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

          if( available.length > 0 ){
            const err = new HttpErrors.PreconditionFailed(`'where' conditions can only contain ${JSON.stringify(available)} , but now where is: ${JSON.stringify(rawWhere)}`);
            let ok = true;
            for (let index = 0; index < allField.length; index++) {
              const er = allField[index];
              ok = available.includes(er);
              if (!ok) { break }
            }
            if (!ok) {
              throw err;
            }
          }

        <%_ if (val['filter']) { -%>
            filter.where = filter.where || {};
            Object.assign(filter.where, <%- JSON.stringify(val['filter']) %>);
        <%_ } -%>
        <%_ if (val['fields']) { -%>
            filter.fields = filter.fields || {};
            Object.assign(filter.fields, <%- JSON.stringify(val['fields']) %>);
        <%_ } -%>
        let aliasNameMapping = <%- JSON.stringify(originalConfig.nameAliasMapping)%>;

        let where = {};
        if(filter['where']){
            where = this.<%= repositoryNameCamel %>.dataSource.connector.buildWhere(this.<%= repositoryNameCamel %>.entityClass.definition.name,filter['where']);
        }
        filter['where'] = where || filter['where'] || {};
        let resultTotal = await this.<%= repositoryNameCamel %>.count(filter['where']);
        //@ts-ignore
        if (resultTotal <= 0) {
            let empty: Record<string, any> = [];
            //@ts-ignore
            return {empty, resultTotal};
        }
        //@ts-ignore
        let dataInfo = await this.<%= repositoryNameCamel %>.find(filter)
        let resultData = handleNameMappingAll(dataInfo, aliasNameMapping);
        //@ts-ignore
        return {data: resultData, total: resultTotal};
      }

    <%_ } -%>
  <%_ }) -%>

  // @ts-ignore
  findAllField(where, allField) {
    if (!where || !allField) { return }
    const oa = where.or || where.and;
    if (oa && Object.prototype.toString.call(oa) === '[object Array]') {
      oa.forEach((item:any) => {
        this.findAllField(item, allField);
      });
    } else {
      for (const key in where) {
        if (key && where.hasOwnProperty(key)) {
          allField.push(key);
        }
      }
    }
  }

}

function js_traverse(o:any) {
  let type = typeof o
  if (type === "object") {
    for (let key in o) {
        let itemType = typeof o[key];
        if (itemType === "object" && o[key] && "Decimal128" === o[key]._bsontype) {
         o[key] = o[key].toString();
         continue;
        }
      else if(key !== '__proto__' && o.hasOwnProperty(key)){
        try{
          o[key] = js_traverse(o[key])
        }catch(e){
          console.error(e);
        }
      }
    }
  } else {
  }
  return o;
}

function filterFields(filterObject: any , assignFunction: (input: Record<string, any>) => void) : Record<string, any> {
    //以全局配置为最大查询范围
    let globalFields: Record<string, any> = {};
    assignFunction(globalFields);
    let globalKeys: string[] = [];
    let global: Record<string, boolean> = {};
    Object.keys(globalFields).forEach(key => {
        let value: any = globalFields[key];
        if (value === "true" || value === "1" || value === true || value === 1) {
            globalKeys.push(key);
            global[key] = true;
        }
    });

    let queryFields: Record<string, boolean> = {};
    if( typeof filterObject.fields === 'object' && !Array.isArray(filterObject.fields) && Object.keys(filterObject.fields).length > 0){
        //格式一： {'f1':true, 'f2':1}
        const fieldsObj = filterObject.fields as Record<string, any>;
        Object.keys(fieldsObj).forEach(key => {
            if (globalKeys.indexOf(key) !== -1) {
                let value: any = fieldsObj[key];
                queryFields[key] = (value === "true" || value === "1" || value === true || value === 1);
            }
        });
    } else if (Array.isArray(filterObject.fields) && filterObject.fields.length > 0) {
        //格式二： ['f1', 'f2']
        for (let i = 0; i < filterObject.fields.length; i++) {
            let fName: any = filterObject.fields[i];
            if (typeof fName === 'string' && globalKeys.indexOf(fName) !== -1) {
                queryFields[fName] = true;
            }
        }
    } else {
        queryFields = global;
    }
    if (Object.keys(queryFields).length <= 0) {
        queryFields = global;
    }

    let filter = filterObject;

    if (Object.keys(queryFields).length > 0) {
        filter.fields = queryFields;
        //如果filter.fields既包含true又包含false,则移除false的字段
        if (typeof filter.fields === 'object' && !Array.isArray(filter.fields)) {
            let tag: number = 0;
            let allIsTrueOrFalse: boolean = true;
            const fieldsObj = filter.fields as Record<string, any>;
            let keysOfFields: string[] = Object.keys(fieldsObj);
            for(let index=0; index < keysOfFields.length; index++) {
                let keyOfField: string = keysOfFields[index];
                let valueOfField: any = fieldsObj[keyOfField];
                if (valueOfField === "true" || valueOfField === "1" || valueOfField === true || valueOfField === 1) {
                    tag += 1;
                } else {
                    tag -= 1;
                }
                if (tag != (-1 - index) && tag != (1 + index)) {
                    allIsTrueOrFalse = false;
                    break;
                }
            }
            if (!allIsTrueOrFalse) {
                for(let index=0; index < keysOfFields.length; index++) {
                    let keyOfField: string = keysOfFields[index];
                    let valueOfField: any = fieldsObj[keyOfField];
                    if (!(valueOfField === "true" || valueOfField === "1" || valueOfField === true || valueOfField === 1)) {
                        delete fieldsObj[keyOfField];
                    }
                }
            } else {
                //仅false时，合并全局参数
                if (!fieldsObj[keysOfFields[0]]) {
                    const filterKeys = Object.keys(filter.fields);
                    for (let index = 0; index < globalKeys.length; index++) {
                        const key = globalKeys[index];
                        if (filterKeys.indexOf(key) === -1) {
                            filter.fields[key] = true;
                        }
                    }
                }
            }
        }
    } else {
        //兼容旧配置，新版globalFields不会是空的，前端会做验证。
        delete filter.fields;
    }
   return filter;
}

function handleNameMappingAll(data: Array<Record<string, any>>, nameMapping: Record<string, string>) : Array<Record<string, any>> {
    if (!data || !nameMapping) {
        return data;
    }
    let newData : Array<Record<string, any>> = [];
    data.forEach(value => {
        newData.push(applyMapping(value, nameMapping));
    })
    return newData;
}

type JSONValue = string | number | boolean | JSONObject | JSONArray;
interface JSONObject { [key: string]: JSONValue; }
interface JSONArray extends Array<JSONValue> { }

function getDeepValue(obj: any, path: string): any {
    const parts = path.split(".");
    if (parts.length === 1) {
        return obj[parts[0]];
    }
    let current = obj;
    for (const part of parts) {
        if (current == null) return undefined;
        current = current[part];
    }
    return current;
}

function setDeepValue(obj: any, path: string, value: any): void {
    const parts = path.split(".");
    if (parts.length === 1) {
        obj[parts[0]] = value;
        return
    }
    let current = obj;
    for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (
            typeof current[part] !== "object" ||
            Array.isArray(current[part])
        ) {
            current[part] = {};
        }
        current = current[part];
    }
    const last = parts[parts.length - 1];
    current[last] = value;
}

function deepClone(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
}

function deepEach(obj: any) {
    if (obj === 'undefined' || obj === null) {
        return obj;
    }
    // Handle Date objects specifically
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    // Handle arrays before general objects
    if (Array.isArray(obj)) {
       let newArr : [] = [];
       for (let i = 0; i < obj.length; i++) {
           //@ts-ignore
           newArr.push(deepEach(obj[i]));
       }
       return newArr;
    }
    // Handle Map objects specifically
    if (obj instanceof Map) {
        let newMap = new Map();
        obj.forEach((value, key) => {
            newMap.set(key, deepEach(value));
        });
        return newMap;
    }
    // Handle Set objects specifically
    if (obj instanceof Set) {
        let newSet = new Set();
        obj.forEach(value => {
            newSet.add(deepEach(value));
        });
        return newSet;
    }
    // Handle plain objects and Records (Record<K,V> is just a plain object at runtime)
    if (typeof obj === 'object' && obj.constructor === Object) {
        let newObj : Record<string, any> = {};
        Object.keys(obj).forEach(key => {
            newObj[key] = deepEach(obj[key]);
        })
        return newObj;
    }
    // Handle other object types (like custom classes)
    if (typeof obj === 'object') {
        // For custom objects, try to create a new instance of the same constructor
        try {
            let newObj = Object.create(Object.getPrototypeOf(obj));
            Object.keys(obj).forEach(key => {
                newObj[key] = deepEach(obj[key]);
            });
            return newObj;
        } catch (e) {
            return obj;
        }
    }
    return obj;
}

function mergeObjects(target: any, source: any): any {
    if (!target || !source) {
        return target || source || {};
    }
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            const sourceValue = source[key];

            // Handle null values
            if (sourceValue === null) {
                target[key] = null;
                continue;
            }

            // Handle non-object values (primitives, functions)
            if (typeof sourceValue !== "object") {
                target[key] = sourceValue;
                continue;
            }

            // Handle arrays
            if (Array.isArray(sourceValue)) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Date objects
            if (sourceValue instanceof Date) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Map objects
            if (sourceValue instanceof Map) {
                target[key] = sourceValue;
                continue;
            }

            // Handle Set objects
            if (sourceValue instanceof Set) {
                target[key] = sourceValue;
                continue;
            }

            // Handle plain objects only (not custom class instances)
            if (sourceValue.constructor === Object) {
                if (!target[key] || typeof target[key] !== "object" || Array.isArray(target[key]) || target[key].constructor !== Object) {
                    target[key] = {};
                }
                mergeObjects(target[key], sourceValue);
            } else {
                // For custom objects, just assign directly to preserve their type
                target[key] = sourceValue;
            }
        }
    }
    return target;
}

function applyMapping(input: JSONObject, mapping: Record<string, string>): JSONObject {
    const baseResult = {};
    const untouchedResult = removeMappingKey(deepClone(input), mapping);
    for (const [fromPath, toPath] of Object.entries(mapping)) {
        const value = getDeepValue(input, fromPath);
        if (value === undefined) continue;
        const fromParts = fromPath.split(".");
        const lastKey = fromParts[fromParts.length - 1];
        const parentPath = fromParts.slice(0, -1).join(".");
        const parent = parentPath ? getDeepValue(input, parentPath) : input;
        if (Array.isArray(parent)) {
            const arr: any[] = [];
            for (const item of parent) {
                if (item && typeof item === "object" && lastKey in item) {
                arr.push(item[lastKey]);
                }
            }
            setDeepValue(baseResult, toPath, arr);
        } else {
            setDeepValue(baseResult, toPath, value);
        }
    }
    return mergeObjects(untouchedResult, baseResult);
}

function removeMappingKey(untouchedResult: any, mapping: Record<string, string>) : any {
    Object.keys(mapping).forEach(key => {
        let paths = key.split(".")
        if (paths.length === 1) {
            delete untouchedResult[paths[0]];
        } else {
            let needRemove : any[] = [];
            deepCollect(needRemove, untouchedResult, paths, 0)
            needRemove.forEach(item => {
                delete item[paths[paths.length - 1]];
            })
        }
    })
    return untouchedResult;
}

function deepCollect(collect: any[], map : any, paths: string[], index : number) {
    if (paths.length - 1 >= index) {
        return
    }
    if (typeof map !== 'object') {
        return;
    }
    let path : string = paths[index];
    let value : any = map[path]
    if (Array.isArray(value)) {
        value.forEach(item => {
            deepCollect(collect, item, paths, index + 1);
        })
    } else if (typeof value === 'object') {
        collect.push(value);
        return;
    } else {
        return;
    }
}